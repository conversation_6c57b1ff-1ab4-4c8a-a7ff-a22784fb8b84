language: c

compiler:
  - gcc

env:
  global:
   # The next declaration is the encrypted COVERITY_SCAN_TOKEN, created
   #   via the "travis encrypt" command using the project repo's public key
   - secure: "pl/hj6AV6k4VmtdjNo0U0V2gOPgGnv/FFDWe2IK6Hx2ewTOE+i1uWk4EHASG/yTl8KV04u5IJ1RG7uXOeLtnArxPe1+H6EnU+BMHSO1pP1+nOCWUcMnWSjaoaZlXZILPWMs+1TJMHe4yhd0O5KI76CF35daTuR2RHWaMLiwJEjwm+IaLPqeCzVuUFmJdi+dZZUuycD40olP1ehI1/DMwZbEP1cmN0WCGde2F7+nebUK6gY6LVkyUye/glNwjiW1R2TrOfJQ7TdKMPFWfD/3N2naa+t4eg8NAEE0vNvGWv/eNvizJAE3AmjV5oXpWqNzvcNL6ItfAi5PdNckjlwnShAQViDFLzhrrBVRh7e1SsToSgat5mxuP848O0lYNzaAK3xTun/w2ouOyWt41HhctsbGUY3Z4wl38TEDfofLWF7Y+AbSa2/rc9pvd8RqnApMXIpHrDZv+6qGYMcMntxbAIoSltDC9EsxI88jCa6Az74Aeo3mryCVwi2/26SVGjezw5xxfhaGKyG9K9108V24LKXQDZNDwTRe1z3rOdg/FCXVXnLCDutsjYwVSTurFNQEz6VeUwII1G7WqLDrTNcUcJTXfjUgyTXATyk1NpbEjUtrPWw8bbys1bBrATOpzjQIaUC9RFYEcIFU2WezkdgnQQn2oUqP9QRx0TRtwvtghQVg="

before_install:
      - echo -n | openssl s_client -connect scan.coverity.com:443 | sed -ne '/-BEGIN CERTIFICATE-/,/-END CERTIFICATE-/p' | sudo tee -a /etc/ssl/certs/ca-

before_install:
  - echo -n | openssl s_client -connect scan.coverity.com:443 | sed -ne '/-BEGIN CERTIFICATE-/,/-END CERTIFICATE-/p' | sudo tee -a /etc/ssl/certs/ca-
  - sudo apt-get update
  # Install dependencies required for configuration
  - sudo apt-get install autoconf libtool pkg-config 
  # Upgrade gcc
  - gcc --version
  # Install dependencies
  - sudo apt-get install libatlas-base-dev libfftw3-dev
  
install :
  - sh autogen.sh
  
script :
  - ./configure
  - make

addons:
  coverity_scan:
    project:
      name: "astromatic/sextractor"
      description: "Build submitted via Travis CI"
    notification_email: <EMAIL>
    build_command_prepend: "./configure; make clean"
    build_command:   "make -j 4"
    branch_pattern: coverity_scan

os : 
  - linux
