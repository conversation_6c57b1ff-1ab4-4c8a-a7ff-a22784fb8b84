# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.31

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "MinGW Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "C:/cmake/share/cmake-3.31/Modules/CMakeCCompiler.cmake.in"
  "C:/cmake/share/cmake-3.31/Modules/CMakeCCompilerABI.c"
  "C:/cmake/share/cmake-3.31/Modules/CMakeCInformation.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeCompilerIdDetection.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerSupport.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineRCCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeFindBinUtils.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeGenericSystem.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeInitializeConfigs.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeMinGWFindMake.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeParseImplicitLinkInfo.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeParseLibraryArchitecture.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeRCCompiler.cmake.in"
  "C:/cmake/share/cmake-3.31/Modules/CMakeRCInformation.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeSystem.cmake.in"
  "C:/cmake/share/cmake-3.31/Modules/CMakeSystemSpecificInformation.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeSystemSpecificInitialize.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeTestCompilerCommon.cmake"
  "C:/cmake/share/cmake-3.31/Modules/CMakeTestRCCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/CrayClang-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/GNU-C-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/GNU-C.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/GNU-FindBinUtils.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/GNU.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/HP-C-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/LCC-C-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/NVHPC-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/OrangeC-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/TI-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/TIClang-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/Tasking-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/XL-C-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Compiler/zOS-C-DetermineCompiler.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Internal/CMakeCLinkerInformation.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Internal/CMakeInspectCLinker.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Internal/FeatureTesting.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Linker/GNU-C.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Linker/GNU.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Platform/Linker/GNU.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Platform/Linker/Windows-GNU-C.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Platform/Linker/Windows-GNU.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Platform/Windows-GNU-C-ABI.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Platform/Windows-GNU-C.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Platform/Windows-GNU.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Platform/Windows-Initialize.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Platform/Windows-windres.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Platform/Windows.cmake"
  "C:/cmake/share/cmake-3.31/Modules/Platform/WindowsPaths.cmake"
  "E:/github/sextractor_win/win/CMakeLists.txt"
  "CMakeFiles/3.31.8/CMakeCCompiler.cmake"
  "CMakeFiles/3.31.8/CMakeRCCompiler.cmake"
  "CMakeFiles/3.31.8/CMakeSystem.cmake"
  "E:/github/sextractor_win/win/config.h.in"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/3.31.8/CMakeSystem.cmake"
  "CMakeFiles/3.31.8/CMakeCCompiler.cmake"
  "CMakeFiles/3.31.8/CMakeRCCompiler.cmake"
  "CMakeFiles/3.31.8/CMakeCCompiler.cmake"
  "CMakeFiles/3.31.8/CMakeCCompiler.cmake"
  "config.h"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/fits.dir/DependInfo.cmake"
  "CMakeFiles/wcs.dir/DependInfo.cmake"
  "CMakeFiles/levmar.dir/DependInfo.cmake"
  "CMakeFiles/sex.dir/DependInfo.cmake"
  )
