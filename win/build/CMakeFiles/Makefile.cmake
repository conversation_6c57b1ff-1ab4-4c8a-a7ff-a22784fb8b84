# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "MinGW Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeLanguageInformation.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeRCInformation.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-C.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Linker/GNU-C.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Linker/GNU.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU-C.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU-C-ABI.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU-C.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-Initialize.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-windres.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake"
  "E:/github/sextractor_win/win/CMakeLists.txt"
  "CMakeFiles/4.1.0/CMakeCCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeRCCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeSystem.cmake"
  "E:/github/sextractor_win/win/config.h.in"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "config.h"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/fits.dir/DependInfo.cmake"
  "CMakeFiles/wcs.dir/DependInfo.cmake"
  "CMakeFiles/levmar.dir/DependInfo.cmake"
  "CMakeFiles/sex.dir/DependInfo.cmake"
  )
