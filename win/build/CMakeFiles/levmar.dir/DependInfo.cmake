
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "E:/github/sextractor_win/src/levmar/Axb.c" "CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/Axb.c.obj" "gcc" "CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/Axb.c.obj.d"
  "E:/github/sextractor_win/src/levmar/lm.c" "CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lm.c.obj" "gcc" "CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lm.c.obj.d"
  "E:/github/sextractor_win/src/levmar/lmbc.c" "CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbc.c.obj" "gcc" "CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbc.c.obj.d"
  "E:/github/sextractor_win/src/levmar/lmblec.c" "CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmblec.c.obj" "gcc" "CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmblec.c.obj.d"
  "E:/github/sextractor_win/src/levmar/lmbleic.c" "CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbleic.c.obj" "gcc" "CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbleic.c.obj.d"
  "E:/github/sextractor_win/src/levmar/lmlec.c" "CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmlec.c.obj" "gcc" "CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmlec.c.obj.d"
  "E:/github/sextractor_win/src/levmar/misc.c" "CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/misc.c.obj" "gcc" "CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/misc.c.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
