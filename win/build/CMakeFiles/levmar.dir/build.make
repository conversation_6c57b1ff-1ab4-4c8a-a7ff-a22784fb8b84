# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = C:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\github\sextractor_win\win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\github\sextractor_win\win\build

# Include any dependencies generated for this target.
include CMakeFiles/levmar.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/levmar.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/levmar.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/levmar.dir/flags.make

CMakeFiles/levmar.dir/codegen:
.PHONY : CMakeFiles/levmar.dir/codegen

CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/Axb.c.obj: CMakeFiles/levmar.dir/flags.make
CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/Axb.c.obj: CMakeFiles/levmar.dir/includes_C.rsp
CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/Axb.c.obj: E:/github/sextractor_win/src/levmar/Axb.c
CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/Axb.c.obj: CMakeFiles/levmar.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/Axb.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/Axb.c.obj -MF CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\Axb.c.obj.d -o CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\Axb.c.obj -c E:\github\sextractor_win\src\levmar\Axb.c

CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/Axb.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/Axb.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\levmar\Axb.c > CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\Axb.c.i

CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/Axb.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/Axb.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\levmar\Axb.c -o CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\Axb.c.s

CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lm.c.obj: CMakeFiles/levmar.dir/flags.make
CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lm.c.obj: CMakeFiles/levmar.dir/includes_C.rsp
CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lm.c.obj: E:/github/sextractor_win/src/levmar/lm.c
CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lm.c.obj: CMakeFiles/levmar.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lm.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lm.c.obj -MF CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\lm.c.obj.d -o CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\lm.c.obj -c E:\github\sextractor_win\src\levmar\lm.c

CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lm.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lm.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\levmar\lm.c > CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\lm.c.i

CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lm.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lm.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\levmar\lm.c -o CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\lm.c.s

CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbc.c.obj: CMakeFiles/levmar.dir/flags.make
CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbc.c.obj: CMakeFiles/levmar.dir/includes_C.rsp
CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbc.c.obj: E:/github/sextractor_win/src/levmar/lmbc.c
CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbc.c.obj: CMakeFiles/levmar.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbc.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbc.c.obj -MF CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\lmbc.c.obj.d -o CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\lmbc.c.obj -c E:\github\sextractor_win\src\levmar\lmbc.c

CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbc.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\levmar\lmbc.c > CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\lmbc.c.i

CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbc.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\levmar\lmbc.c -o CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\lmbc.c.s

CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmblec.c.obj: CMakeFiles/levmar.dir/flags.make
CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmblec.c.obj: CMakeFiles/levmar.dir/includes_C.rsp
CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmblec.c.obj: E:/github/sextractor_win/src/levmar/lmblec.c
CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmblec.c.obj: CMakeFiles/levmar.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmblec.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmblec.c.obj -MF CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\lmblec.c.obj.d -o CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\lmblec.c.obj -c E:\github\sextractor_win\src\levmar\lmblec.c

CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmblec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmblec.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\levmar\lmblec.c > CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\lmblec.c.i

CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmblec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmblec.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\levmar\lmblec.c -o CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\lmblec.c.s

CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbleic.c.obj: CMakeFiles/levmar.dir/flags.make
CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbleic.c.obj: CMakeFiles/levmar.dir/includes_C.rsp
CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbleic.c.obj: E:/github/sextractor_win/src/levmar/lmbleic.c
CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbleic.c.obj: CMakeFiles/levmar.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbleic.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbleic.c.obj -MF CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\lmbleic.c.obj.d -o CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\lmbleic.c.obj -c E:\github\sextractor_win\src\levmar\lmbleic.c

CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbleic.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbleic.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\levmar\lmbleic.c > CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\lmbleic.c.i

CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbleic.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbleic.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\levmar\lmbleic.c -o CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\lmbleic.c.s

CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmlec.c.obj: CMakeFiles/levmar.dir/flags.make
CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmlec.c.obj: CMakeFiles/levmar.dir/includes_C.rsp
CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmlec.c.obj: E:/github/sextractor_win/src/levmar/lmlec.c
CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmlec.c.obj: CMakeFiles/levmar.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmlec.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmlec.c.obj -MF CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\lmlec.c.obj.d -o CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\lmlec.c.obj -c E:\github\sextractor_win\src\levmar\lmlec.c

CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmlec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmlec.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\levmar\lmlec.c > CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\lmlec.c.i

CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmlec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmlec.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\levmar\lmlec.c -o CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\lmlec.c.s

CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/misc.c.obj: CMakeFiles/levmar.dir/flags.make
CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/misc.c.obj: CMakeFiles/levmar.dir/includes_C.rsp
CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/misc.c.obj: E:/github/sextractor_win/src/levmar/misc.c
CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/misc.c.obj: CMakeFiles/levmar.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/misc.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/misc.c.obj -MF CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\misc.c.obj.d -o CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\misc.c.obj -c E:\github\sextractor_win\src\levmar\misc.c

CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/misc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/misc.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\levmar\misc.c > CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\misc.c.i

CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/misc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/misc.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\levmar\misc.c -o CMakeFiles\levmar.dir\E_\github\sextractor_win\src\levmar\misc.c.s

# Object files for target levmar
levmar_OBJECTS = \
"CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/Axb.c.obj" \
"CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lm.c.obj" \
"CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbc.c.obj" \
"CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmblec.c.obj" \
"CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbleic.c.obj" \
"CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmlec.c.obj" \
"CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/misc.c.obj"

# External object files for target levmar
levmar_EXTERNAL_OBJECTS =

liblevmar.a: CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/Axb.c.obj
liblevmar.a: CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lm.c.obj
liblevmar.a: CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbc.c.obj
liblevmar.a: CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmblec.c.obj
liblevmar.a: CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbleic.c.obj
liblevmar.a: CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmlec.c.obj
liblevmar.a: CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/misc.c.obj
liblevmar.a: CMakeFiles/levmar.dir/build.make
liblevmar.a: CMakeFiles/levmar.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Linking C static library liblevmar.a"
	$(CMAKE_COMMAND) -P CMakeFiles\levmar.dir\cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\levmar.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/levmar.dir/build: liblevmar.a
.PHONY : CMakeFiles/levmar.dir/build

CMakeFiles/levmar.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\levmar.dir\cmake_clean.cmake
.PHONY : CMakeFiles/levmar.dir/clean

CMakeFiles/levmar.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" E:\github\sextractor_win\win E:\github\sextractor_win\win E:\github\sextractor_win\win\build E:\github\sextractor_win\win\build E:\github\sextractor_win\win\build\CMakeFiles\levmar.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/levmar.dir/depend

