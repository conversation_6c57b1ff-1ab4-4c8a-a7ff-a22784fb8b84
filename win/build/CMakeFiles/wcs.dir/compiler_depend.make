# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/cel.c.obj: E:/github/sextractor_win/src/wcs/cel.c \
  C:/msys64/mingw64/include/_mingw.h \
  C:/msys64/mingw64/include/_mingw_mac.h \
  C:/msys64/mingw64/include/_mingw_off_t.h \
  C:/msys64/mingw64/include/_mingw_secapi.h \
  C:/msys64/mingw64/include/corecrt.h \
  C:/msys64/mingw64/include/corecrt_stdio_config.h \
  C:/msys64/mingw64/include/corecrt_wstdlib.h \
  C:/msys64/mingw64/include/crtdefs.h \
  C:/msys64/mingw64/include/direct.h \
  C:/msys64/mingw64/include/errno.h \
  C:/msys64/mingw64/include/float.h \
  C:/msys64/mingw64/include/io.h \
  C:/msys64/mingw64/include/limits.h \
  C:/msys64/mingw64/include/malloc.h \
  C:/msys64/mingw64/include/math.h \
  C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
  C:/msys64/mingw64/include/sec_api/stdio_s.h \
  C:/msys64/mingw64/include/sec_api/stdlib_s.h \
  C:/msys64/mingw64/include/sec_api/string_s.h \
  C:/msys64/mingw64/include/stdio.h \
  C:/msys64/mingw64/include/stdlib.h \
  C:/msys64/mingw64/include/string.h \
  C:/msys64/mingw64/include/swprintf.inl \
  C:/msys64/mingw64/include/vadefs.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/float.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
  config.h \
  E:/github/sextractor_win/src/wcs/cel.h \
  E:/github/sextractor_win/src/wcs/proj.h \
  E:/github/sextractor_win/src/wcs/sph.h \
  E:/github/sextractor_win/src/wcs/tnx.h \
  E:/github/sextractor_win/src/wcs/wcstrig.h

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/lin.c.obj: E:/github/sextractor_win/src/wcs/lin.c \
  C:/msys64/mingw64/include/_mingw.h \
  C:/msys64/mingw64/include/_mingw_mac.h \
  C:/msys64/mingw64/include/_mingw_off_t.h \
  C:/msys64/mingw64/include/_mingw_secapi.h \
  C:/msys64/mingw64/include/corecrt.h \
  C:/msys64/mingw64/include/corecrt_stdio_config.h \
  C:/msys64/mingw64/include/corecrt_wstdlib.h \
  C:/msys64/mingw64/include/crtdefs.h \
  C:/msys64/mingw64/include/direct.h \
  C:/msys64/mingw64/include/errno.h \
  C:/msys64/mingw64/include/float.h \
  C:/msys64/mingw64/include/io.h \
  C:/msys64/mingw64/include/limits.h \
  C:/msys64/mingw64/include/malloc.h \
  C:/msys64/mingw64/include/math.h \
  C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
  C:/msys64/mingw64/include/sec_api/stdio_s.h \
  C:/msys64/mingw64/include/sec_api/stdlib_s.h \
  C:/msys64/mingw64/include/sec_api/string_s.h \
  C:/msys64/mingw64/include/stdio.h \
  C:/msys64/mingw64/include/stdlib.h \
  C:/msys64/mingw64/include/string.h \
  C:/msys64/mingw64/include/swprintf.inl \
  C:/msys64/mingw64/include/vadefs.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/float.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
  config.h \
  E:/github/sextractor_win/src/wcs/lin.h

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/poly.c.obj: E:/github/sextractor_win/src/wcs/poly.c \
  C:/msys64/mingw64/include/_mingw.h \
  C:/msys64/mingw64/include/_mingw_mac.h \
  C:/msys64/mingw64/include/_mingw_off_t.h \
  C:/msys64/mingw64/include/_mingw_secapi.h \
  C:/msys64/mingw64/include/corecrt.h \
  C:/msys64/mingw64/include/corecrt_stdio_config.h \
  C:/msys64/mingw64/include/corecrt_wstdlib.h \
  C:/msys64/mingw64/include/crtdefs.h \
  C:/msys64/mingw64/include/direct.h \
  C:/msys64/mingw64/include/errno.h \
  C:/msys64/mingw64/include/float.h \
  C:/msys64/mingw64/include/io.h \
  C:/msys64/mingw64/include/limits.h \
  C:/msys64/mingw64/include/malloc.h \
  C:/msys64/mingw64/include/math.h \
  C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
  C:/msys64/mingw64/include/sec_api/stdio_s.h \
  C:/msys64/mingw64/include/sec_api/stdlib_s.h \
  C:/msys64/mingw64/include/sec_api/string_s.h \
  C:/msys64/mingw64/include/stdio.h \
  C:/msys64/mingw64/include/stdlib.h \
  C:/msys64/mingw64/include/string.h \
  C:/msys64/mingw64/include/swprintf.inl \
  C:/msys64/mingw64/include/vadefs.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/float.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
  config.h \
  E:/github/sextractor_win/src/wcs/poly.h

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/proj.c.obj: E:/github/sextractor_win/src/wcs/proj.c \
  C:/msys64/mingw64/include/_mingw.h \
  C:/msys64/mingw64/include/_mingw_mac.h \
  C:/msys64/mingw64/include/_mingw_off_t.h \
  C:/msys64/mingw64/include/_mingw_secapi.h \
  C:/msys64/mingw64/include/corecrt.h \
  C:/msys64/mingw64/include/corecrt_stdio_config.h \
  C:/msys64/mingw64/include/corecrt_wstdlib.h \
  C:/msys64/mingw64/include/crtdefs.h \
  C:/msys64/mingw64/include/direct.h \
  C:/msys64/mingw64/include/errno.h \
  C:/msys64/mingw64/include/float.h \
  C:/msys64/mingw64/include/io.h \
  C:/msys64/mingw64/include/limits.h \
  C:/msys64/mingw64/include/malloc.h \
  C:/msys64/mingw64/include/math.h \
  C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
  C:/msys64/mingw64/include/sec_api/stdio_s.h \
  C:/msys64/mingw64/include/sec_api/stdlib_s.h \
  C:/msys64/mingw64/include/sec_api/string_s.h \
  C:/msys64/mingw64/include/stdio.h \
  C:/msys64/mingw64/include/stdlib.h \
  C:/msys64/mingw64/include/string.h \
  C:/msys64/mingw64/include/swprintf.inl \
  C:/msys64/mingw64/include/vadefs.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/float.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
  config.h \
  E:/github/sextractor_win/src/wcs/poly.h \
  E:/github/sextractor_win/src/wcs/proj.h \
  E:/github/sextractor_win/src/wcs/tnx.h \
  E:/github/sextractor_win/src/wcs/wcsmath.h \
  E:/github/sextractor_win/src/wcs/wcstrig.h

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/sph.c.obj: E:/github/sextractor_win/src/wcs/sph.c \
  C:/msys64/mingw64/include/_mingw.h \
  C:/msys64/mingw64/include/_mingw_mac.h \
  C:/msys64/mingw64/include/_mingw_off_t.h \
  C:/msys64/mingw64/include/_mingw_secapi.h \
  C:/msys64/mingw64/include/corecrt.h \
  C:/msys64/mingw64/include/corecrt_stdio_config.h \
  C:/msys64/mingw64/include/corecrt_wstdlib.h \
  C:/msys64/mingw64/include/crtdefs.h \
  C:/msys64/mingw64/include/direct.h \
  C:/msys64/mingw64/include/errno.h \
  C:/msys64/mingw64/include/float.h \
  C:/msys64/mingw64/include/io.h \
  C:/msys64/mingw64/include/limits.h \
  C:/msys64/mingw64/include/malloc.h \
  C:/msys64/mingw64/include/math.h \
  C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
  C:/msys64/mingw64/include/sec_api/stdio_s.h \
  C:/msys64/mingw64/include/sec_api/stdlib_s.h \
  C:/msys64/mingw64/include/sec_api/string_s.h \
  C:/msys64/mingw64/include/stdio.h \
  C:/msys64/mingw64/include/stdlib.h \
  C:/msys64/mingw64/include/string.h \
  C:/msys64/mingw64/include/swprintf.inl \
  C:/msys64/mingw64/include/vadefs.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/float.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
  config.h \
  E:/github/sextractor_win/src/wcs/sph.h \
  E:/github/sextractor_win/src/wcs/wcstrig.h

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/tnx.c.obj: E:/github/sextractor_win/src/wcs/tnx.c \
  C:/msys64/mingw64/include/_mingw.h \
  C:/msys64/mingw64/include/_mingw_mac.h \
  C:/msys64/mingw64/include/_mingw_off_t.h \
  C:/msys64/mingw64/include/_mingw_secapi.h \
  C:/msys64/mingw64/include/corecrt.h \
  C:/msys64/mingw64/include/corecrt_stdio_config.h \
  C:/msys64/mingw64/include/corecrt_wstdlib.h \
  C:/msys64/mingw64/include/crtdefs.h \
  C:/msys64/mingw64/include/direct.h \
  C:/msys64/mingw64/include/errno.h \
  C:/msys64/mingw64/include/float.h \
  C:/msys64/mingw64/include/io.h \
  C:/msys64/mingw64/include/limits.h \
  C:/msys64/mingw64/include/malloc.h \
  C:/msys64/mingw64/include/math.h \
  C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
  C:/msys64/mingw64/include/sec_api/stdio_s.h \
  C:/msys64/mingw64/include/sec_api/stdlib_s.h \
  C:/msys64/mingw64/include/sec_api/string_s.h \
  C:/msys64/mingw64/include/stdio.h \
  C:/msys64/mingw64/include/stdlib.h \
  C:/msys64/mingw64/include/string.h \
  C:/msys64/mingw64/include/swprintf.inl \
  C:/msys64/mingw64/include/vadefs.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/float.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
  config.h \
  E:/github/sextractor_win/src/wcs/tnx.h

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcs.c.obj: E:/github/sextractor_win/src/wcs/wcs.c \
  C:/msys64/mingw64/include/_mingw.h \
  C:/msys64/mingw64/include/_mingw_mac.h \
  C:/msys64/mingw64/include/_mingw_off_t.h \
  C:/msys64/mingw64/include/_mingw_secapi.h \
  C:/msys64/mingw64/include/corecrt.h \
  C:/msys64/mingw64/include/corecrt_stdio_config.h \
  C:/msys64/mingw64/include/corecrt_wstdlib.h \
  C:/msys64/mingw64/include/crtdefs.h \
  C:/msys64/mingw64/include/direct.h \
  C:/msys64/mingw64/include/errno.h \
  C:/msys64/mingw64/include/float.h \
  C:/msys64/mingw64/include/io.h \
  C:/msys64/mingw64/include/limits.h \
  C:/msys64/mingw64/include/malloc.h \
  C:/msys64/mingw64/include/math.h \
  C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
  C:/msys64/mingw64/include/sec_api/stdio_s.h \
  C:/msys64/mingw64/include/sec_api/stdlib_s.h \
  C:/msys64/mingw64/include/sec_api/string_s.h \
  C:/msys64/mingw64/include/stdio.h \
  C:/msys64/mingw64/include/stdlib.h \
  C:/msys64/mingw64/include/string.h \
  C:/msys64/mingw64/include/swprintf.inl \
  C:/msys64/mingw64/include/vadefs.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/float.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
  config.h \
  E:/github/sextractor_win/src/wcs/cel.h \
  E:/github/sextractor_win/src/wcs/lin.h \
  E:/github/sextractor_win/src/wcs/proj.h \
  E:/github/sextractor_win/src/wcs/sph.h \
  E:/github/sextractor_win/src/wcs/wcs.h \
  E:/github/sextractor_win/src/wcs/wcsmath.h \
  E:/github/sextractor_win/src/wcs/wcstrig.h

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcstrig.c.obj: E:/github/sextractor_win/src/wcs/wcstrig.c \
  C:/msys64/mingw64/include/_mingw.h \
  C:/msys64/mingw64/include/_mingw_mac.h \
  C:/msys64/mingw64/include/_mingw_off_t.h \
  C:/msys64/mingw64/include/_mingw_secapi.h \
  C:/msys64/mingw64/include/corecrt.h \
  C:/msys64/mingw64/include/corecrt_stdio_config.h \
  C:/msys64/mingw64/include/corecrt_wstdlib.h \
  C:/msys64/mingw64/include/crtdefs.h \
  C:/msys64/mingw64/include/direct.h \
  C:/msys64/mingw64/include/errno.h \
  C:/msys64/mingw64/include/float.h \
  C:/msys64/mingw64/include/io.h \
  C:/msys64/mingw64/include/limits.h \
  C:/msys64/mingw64/include/malloc.h \
  C:/msys64/mingw64/include/math.h \
  C:/msys64/mingw64/include/sdks/_mingw_ddk.h \
  C:/msys64/mingw64/include/sec_api/stdio_s.h \
  C:/msys64/mingw64/include/sec_api/stdlib_s.h \
  C:/msys64/mingw64/include/sec_api/string_s.h \
  C:/msys64/mingw64/include/stdio.h \
  C:/msys64/mingw64/include/stdlib.h \
  C:/msys64/mingw64/include/string.h \
  C:/msys64/mingw64/include/swprintf.inl \
  C:/msys64/mingw64/include/vadefs.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/float.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h \
  C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h \
  config.h \
  E:/github/sextractor_win/src/wcs/wcstrig.h


E:/github/sextractor_win/src/wcs/wcs.c:

E:/github/sextractor_win/src/wcs/sph.c:

E:/github/sextractor_win/src/wcs/proj.c:

E:/github/sextractor_win/src/wcs/poly.h:

E:/github/sextractor_win/src/wcs/lin.h:

E:/github/sextractor_win/src/wcs/wcstrig.h:

E:/github/sextractor_win/src/wcs/tnx.h:

E:/github/sextractor_win/src/wcs/sph.h:

E:/github/sextractor_win/src/wcs/tnx.c:

E:/github/sextractor_win/src/wcs/proj.h:

E:/github/sextractor_win/src/wcs/cel.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/limits.h:

C:/msys64/mingw64/include/errno.h:

C:/msys64/mingw64/include/_mingw_off_t.h:

C:/msys64/mingw64/include/io.h:

C:/msys64/mingw64/include/corecrt_stdio_config.h:

C:/msys64/mingw64/include/corecrt.h:

C:/msys64/mingw64/include/_mingw_secapi.h:

E:/github/sextractor_win/src/wcs/wcs.h:

C:/msys64/mingw64/include/_mingw_mac.h:

E:/github/sextractor_win/src/wcs/cel.c:

C:/msys64/mingw64/include/sdks/_mingw_ddk.h:

C:/msys64/mingw64/include/vadefs.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/syslimits.h:

C:/msys64/mingw64/include/crtdefs.h:

C:/msys64/mingw64/include/_mingw.h:

C:/msys64/mingw64/include/limits.h:

C:/msys64/mingw64/include/stdlib.h:

C:/msys64/mingw64/include/malloc.h:

E:/github/sextractor_win/src/wcs/wcstrig.c:

E:/github/sextractor_win/src/wcs/poly.c:

C:/msys64/mingw64/include/sec_api/stdio_s.h:

C:/msys64/mingw64/include/corecrt_wstdlib.h:

C:/msys64/mingw64/include/sec_api/stdlib_s.h:

E:/github/sextractor_win/src/wcs/wcsmath.h:

C:/msys64/mingw64/include/stdio.h:

C:/msys64/mingw64/include/sec_api/string_s.h:

C:/msys64/mingw64/include/math.h:

C:/msys64/mingw64/include/string.h:

C:/msys64/mingw64/include/swprintf.inl:

C:/msys64/mingw64/include/float.h:

C:/msys64/mingw64/include/direct.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/float.h:

E:/github/sextractor_win/src/wcs/lin.c:

config.h:

C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include/mm_malloc.h:
