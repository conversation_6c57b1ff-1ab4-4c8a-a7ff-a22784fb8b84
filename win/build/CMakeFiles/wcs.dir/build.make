# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = C:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\github\sextractor_win\win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\github\sextractor_win\win\build

# Include any dependencies generated for this target.
include CMakeFiles/wcs.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/wcs.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/wcs.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/wcs.dir/flags.make

CMakeFiles/wcs.dir/codegen:
.PHONY : CMakeFiles/wcs.dir/codegen

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/cel.c.obj: CMakeFiles/wcs.dir/flags.make
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/cel.c.obj: CMakeFiles/wcs.dir/includes_C.rsp
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/cel.c.obj: E:/github/sextractor_win/src/wcs/cel.c
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/cel.c.obj: CMakeFiles/wcs.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/cel.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/cel.c.obj -MF CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\cel.c.obj.d -o CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\cel.c.obj -c E:\github\sextractor_win\src\wcs\cel.c

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/cel.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/cel.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\wcs\cel.c > CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\cel.c.i

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/cel.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/cel.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\wcs\cel.c -o CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\cel.c.s

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/lin.c.obj: CMakeFiles/wcs.dir/flags.make
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/lin.c.obj: CMakeFiles/wcs.dir/includes_C.rsp
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/lin.c.obj: E:/github/sextractor_win/src/wcs/lin.c
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/lin.c.obj: CMakeFiles/wcs.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/lin.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/lin.c.obj -MF CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\lin.c.obj.d -o CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\lin.c.obj -c E:\github\sextractor_win\src\wcs\lin.c

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/lin.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/lin.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\wcs\lin.c > CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\lin.c.i

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/lin.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/lin.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\wcs\lin.c -o CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\lin.c.s

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/poly.c.obj: CMakeFiles/wcs.dir/flags.make
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/poly.c.obj: CMakeFiles/wcs.dir/includes_C.rsp
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/poly.c.obj: E:/github/sextractor_win/src/wcs/poly.c
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/poly.c.obj: CMakeFiles/wcs.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/poly.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/poly.c.obj -MF CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\poly.c.obj.d -o CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\poly.c.obj -c E:\github\sextractor_win\src\wcs\poly.c

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/poly.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/poly.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\wcs\poly.c > CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\poly.c.i

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/poly.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/poly.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\wcs\poly.c -o CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\poly.c.s

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/proj.c.obj: CMakeFiles/wcs.dir/flags.make
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/proj.c.obj: CMakeFiles/wcs.dir/includes_C.rsp
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/proj.c.obj: E:/github/sextractor_win/src/wcs/proj.c
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/proj.c.obj: CMakeFiles/wcs.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/proj.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/proj.c.obj -MF CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\proj.c.obj.d -o CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\proj.c.obj -c E:\github\sextractor_win\src\wcs\proj.c

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/proj.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/proj.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\wcs\proj.c > CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\proj.c.i

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/proj.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/proj.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\wcs\proj.c -o CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\proj.c.s

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/sph.c.obj: CMakeFiles/wcs.dir/flags.make
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/sph.c.obj: CMakeFiles/wcs.dir/includes_C.rsp
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/sph.c.obj: E:/github/sextractor_win/src/wcs/sph.c
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/sph.c.obj: CMakeFiles/wcs.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/sph.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/sph.c.obj -MF CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\sph.c.obj.d -o CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\sph.c.obj -c E:\github\sextractor_win\src\wcs\sph.c

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/sph.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/sph.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\wcs\sph.c > CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\sph.c.i

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/sph.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/sph.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\wcs\sph.c -o CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\sph.c.s

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/tnx.c.obj: CMakeFiles/wcs.dir/flags.make
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/tnx.c.obj: CMakeFiles/wcs.dir/includes_C.rsp
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/tnx.c.obj: E:/github/sextractor_win/src/wcs/tnx.c
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/tnx.c.obj: CMakeFiles/wcs.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/tnx.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/tnx.c.obj -MF CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\tnx.c.obj.d -o CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\tnx.c.obj -c E:\github\sextractor_win\src\wcs\tnx.c

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/tnx.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/tnx.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\wcs\tnx.c > CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\tnx.c.i

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/tnx.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/tnx.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\wcs\tnx.c -o CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\tnx.c.s

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcs.c.obj: CMakeFiles/wcs.dir/flags.make
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcs.c.obj: CMakeFiles/wcs.dir/includes_C.rsp
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcs.c.obj: E:/github/sextractor_win/src/wcs/wcs.c
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcs.c.obj: CMakeFiles/wcs.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcs.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcs.c.obj -MF CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\wcs.c.obj.d -o CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\wcs.c.obj -c E:\github\sextractor_win\src\wcs\wcs.c

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcs.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcs.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\wcs\wcs.c > CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\wcs.c.i

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcs.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcs.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\wcs\wcs.c -o CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\wcs.c.s

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcstrig.c.obj: CMakeFiles/wcs.dir/flags.make
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcstrig.c.obj: CMakeFiles/wcs.dir/includes_C.rsp
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcstrig.c.obj: E:/github/sextractor_win/src/wcs/wcstrig.c
CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcstrig.c.obj: CMakeFiles/wcs.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcstrig.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcstrig.c.obj -MF CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\wcstrig.c.obj.d -o CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\wcstrig.c.obj -c E:\github\sextractor_win\src\wcs\wcstrig.c

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcstrig.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcstrig.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\wcs\wcstrig.c > CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\wcstrig.c.i

CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcstrig.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcstrig.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\wcs\wcstrig.c -o CMakeFiles\wcs.dir\E_\github\sextractor_win\src\wcs\wcstrig.c.s

# Object files for target wcs
wcs_OBJECTS = \
"CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/cel.c.obj" \
"CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/lin.c.obj" \
"CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/poly.c.obj" \
"CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/proj.c.obj" \
"CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/sph.c.obj" \
"CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/tnx.c.obj" \
"CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcs.c.obj" \
"CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcstrig.c.obj"

# External object files for target wcs
wcs_EXTERNAL_OBJECTS =

libwcs.a: CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/cel.c.obj
libwcs.a: CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/lin.c.obj
libwcs.a: CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/poly.c.obj
libwcs.a: CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/proj.c.obj
libwcs.a: CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/sph.c.obj
libwcs.a: CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/tnx.c.obj
libwcs.a: CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcs.c.obj
libwcs.a: CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcstrig.c.obj
libwcs.a: CMakeFiles/wcs.dir/build.make
libwcs.a: CMakeFiles/wcs.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Linking C static library libwcs.a"
	$(CMAKE_COMMAND) -P CMakeFiles\wcs.dir\cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\wcs.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/wcs.dir/build: libwcs.a
.PHONY : CMakeFiles/wcs.dir/build

CMakeFiles/wcs.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\wcs.dir\cmake_clean.cmake
.PHONY : CMakeFiles/wcs.dir/clean

CMakeFiles/wcs.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" E:\github\sextractor_win\win E:\github\sextractor_win\win E:\github\sextractor_win\win\build E:\github\sextractor_win\win\build E:\github\sextractor_win\win\build\CMakeFiles\wcs.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/wcs.dir/depend

