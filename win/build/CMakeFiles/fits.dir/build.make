# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = C:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\github\sextractor_win\win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\github\sextractor_win\win\build

# Include any dependencies generated for this target.
include CMakeFiles/fits.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/fits.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/fits.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/fits.dir/flags.make

CMakeFiles/fits.dir/codegen:
.PHONY : CMakeFiles/fits.dir/codegen

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsbody.c.obj: CMakeFiles/fits.dir/flags.make
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsbody.c.obj: CMakeFiles/fits.dir/includes_C.rsp
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsbody.c.obj: E:/github/sextractor_win/src/fits/fitsbody.c
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsbody.c.obj: CMakeFiles/fits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsbody.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsbody.c.obj -MF CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitsbody.c.obj.d -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitsbody.c.obj -c E:\github\sextractor_win\src\fits\fitsbody.c

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsbody.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsbody.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\fits\fitsbody.c > CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitsbody.c.i

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsbody.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsbody.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\fits\fitsbody.c -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitsbody.c.s

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscat.c.obj: CMakeFiles/fits.dir/flags.make
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscat.c.obj: CMakeFiles/fits.dir/includes_C.rsp
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscat.c.obj: E:/github/sextractor_win/src/fits/fitscat.c
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscat.c.obj: CMakeFiles/fits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscat.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscat.c.obj -MF CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitscat.c.obj.d -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitscat.c.obj -c E:\github\sextractor_win\src\fits\fitscat.c

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscat.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscat.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\fits\fitscat.c > CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitscat.c.i

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscat.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscat.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\fits\fitscat.c -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitscat.c.s

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscheck.c.obj: CMakeFiles/fits.dir/flags.make
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscheck.c.obj: CMakeFiles/fits.dir/includes_C.rsp
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscheck.c.obj: E:/github/sextractor_win/src/fits/fitscheck.c
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscheck.c.obj: CMakeFiles/fits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscheck.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscheck.c.obj -MF CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitscheck.c.obj.d -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitscheck.c.obj -c E:\github\sextractor_win\src\fits\fitscheck.c

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscheck.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscheck.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\fits\fitscheck.c > CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitscheck.c.i

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscheck.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscheck.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\fits\fitscheck.c -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitscheck.c.s

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscleanup.c.obj: CMakeFiles/fits.dir/flags.make
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscleanup.c.obj: CMakeFiles/fits.dir/includes_C.rsp
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscleanup.c.obj: E:/github/sextractor_win/src/fits/fitscleanup.c
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscleanup.c.obj: CMakeFiles/fits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscleanup.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscleanup.c.obj -MF CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitscleanup.c.obj.d -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitscleanup.c.obj -c E:\github\sextractor_win\src\fits\fitscleanup.c

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscleanup.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscleanup.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\fits\fitscleanup.c > CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitscleanup.c.i

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscleanup.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscleanup.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\fits\fitscleanup.c -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitscleanup.c.s

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsconv.c.obj: CMakeFiles/fits.dir/flags.make
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsconv.c.obj: CMakeFiles/fits.dir/includes_C.rsp
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsconv.c.obj: E:/github/sextractor_win/src/fits/fitsconv.c
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsconv.c.obj: CMakeFiles/fits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsconv.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsconv.c.obj -MF CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitsconv.c.obj.d -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitsconv.c.obj -c E:\github\sextractor_win\src\fits\fitsconv.c

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsconv.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsconv.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\fits\fitsconv.c > CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitsconv.c.i

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsconv.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsconv.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\fits\fitsconv.c -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitsconv.c.s

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitshead.c.obj: CMakeFiles/fits.dir/flags.make
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitshead.c.obj: CMakeFiles/fits.dir/includes_C.rsp
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitshead.c.obj: E:/github/sextractor_win/src/fits/fitshead.c
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitshead.c.obj: CMakeFiles/fits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitshead.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitshead.c.obj -MF CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitshead.c.obj.d -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitshead.c.obj -c E:\github\sextractor_win\src\fits\fitshead.c

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitshead.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitshead.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\fits\fitshead.c > CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitshead.c.i

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitshead.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitshead.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\fits\fitshead.c -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitshead.c.s

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitskey.c.obj: CMakeFiles/fits.dir/flags.make
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitskey.c.obj: CMakeFiles/fits.dir/includes_C.rsp
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitskey.c.obj: E:/github/sextractor_win/src/fits/fitskey.c
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitskey.c.obj: CMakeFiles/fits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitskey.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitskey.c.obj -MF CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitskey.c.obj.d -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitskey.c.obj -c E:\github\sextractor_win\src\fits\fitskey.c

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitskey.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitskey.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\fits\fitskey.c > CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitskey.c.i

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitskey.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitskey.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\fits\fitskey.c -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitskey.c.s

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsmisc.c.obj: CMakeFiles/fits.dir/flags.make
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsmisc.c.obj: CMakeFiles/fits.dir/includes_C.rsp
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsmisc.c.obj: E:/github/sextractor_win/src/fits/fitsmisc.c
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsmisc.c.obj: CMakeFiles/fits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsmisc.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsmisc.c.obj -MF CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitsmisc.c.obj.d -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitsmisc.c.obj -c E:\github\sextractor_win\src\fits\fitsmisc.c

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsmisc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsmisc.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\fits\fitsmisc.c > CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitsmisc.c.i

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsmisc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsmisc.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\fits\fitsmisc.c -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitsmisc.c.s

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsread.c.obj: CMakeFiles/fits.dir/flags.make
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsread.c.obj: CMakeFiles/fits.dir/includes_C.rsp
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsread.c.obj: E:/github/sextractor_win/src/fits/fitsread.c
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsread.c.obj: CMakeFiles/fits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsread.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsread.c.obj -MF CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitsread.c.obj.d -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitsread.c.obj -c E:\github\sextractor_win\src\fits\fitsread.c

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsread.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsread.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\fits\fitsread.c > CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitsread.c.i

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsread.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsread.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\fits\fitsread.c -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitsread.c.s

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitstab.c.obj: CMakeFiles/fits.dir/flags.make
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitstab.c.obj: CMakeFiles/fits.dir/includes_C.rsp
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitstab.c.obj: E:/github/sextractor_win/src/fits/fitstab.c
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitstab.c.obj: CMakeFiles/fits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitstab.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitstab.c.obj -MF CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitstab.c.obj.d -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitstab.c.obj -c E:\github\sextractor_win\src\fits\fitstab.c

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitstab.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitstab.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\fits\fitstab.c > CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitstab.c.i

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitstab.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitstab.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\fits\fitstab.c -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitstab.c.s

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsutil.c.obj: CMakeFiles/fits.dir/flags.make
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsutil.c.obj: CMakeFiles/fits.dir/includes_C.rsp
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsutil.c.obj: E:/github/sextractor_win/src/fits/fitsutil.c
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsutil.c.obj: CMakeFiles/fits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsutil.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsutil.c.obj -MF CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitsutil.c.obj.d -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitsutil.c.obj -c E:\github\sextractor_win\src\fits\fitsutil.c

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsutil.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsutil.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\fits\fitsutil.c > CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitsutil.c.i

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsutil.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsutil.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\fits\fitsutil.c -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitsutil.c.s

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitswrite.c.obj: CMakeFiles/fits.dir/flags.make
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitswrite.c.obj: CMakeFiles/fits.dir/includes_C.rsp
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitswrite.c.obj: E:/github/sextractor_win/src/fits/fitswrite.c
CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitswrite.c.obj: CMakeFiles/fits.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitswrite.c.obj"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitswrite.c.obj -MF CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitswrite.c.obj.d -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitswrite.c.obj -c E:\github\sextractor_win\src\fits\fitswrite.c

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitswrite.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitswrite.c.i"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\fits\fitswrite.c > CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitswrite.c.i

CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitswrite.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitswrite.c.s"
	C:\msys64\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\fits\fitswrite.c -o CMakeFiles\fits.dir\E_\github\sextractor_win\src\fits\fitswrite.c.s

# Object files for target fits
fits_OBJECTS = \
"CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsbody.c.obj" \
"CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscat.c.obj" \
"CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscheck.c.obj" \
"CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscleanup.c.obj" \
"CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsconv.c.obj" \
"CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitshead.c.obj" \
"CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitskey.c.obj" \
"CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsmisc.c.obj" \
"CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsread.c.obj" \
"CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitstab.c.obj" \
"CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsutil.c.obj" \
"CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitswrite.c.obj"

# External object files for target fits
fits_EXTERNAL_OBJECTS =

libfits.a: CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsbody.c.obj
libfits.a: CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscat.c.obj
libfits.a: CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscheck.c.obj
libfits.a: CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscleanup.c.obj
libfits.a: CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsconv.c.obj
libfits.a: CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitshead.c.obj
libfits.a: CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitskey.c.obj
libfits.a: CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsmisc.c.obj
libfits.a: CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsread.c.obj
libfits.a: CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitstab.c.obj
libfits.a: CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsutil.c.obj
libfits.a: CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitswrite.c.obj
libfits.a: CMakeFiles/fits.dir/build.make
libfits.a: CMakeFiles/fits.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Linking C static library libfits.a"
	$(CMAKE_COMMAND) -P CMakeFiles\fits.dir\cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\fits.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/fits.dir/build: libfits.a
.PHONY : CMakeFiles/fits.dir/build

CMakeFiles/fits.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\fits.dir\cmake_clean.cmake
.PHONY : CMakeFiles/fits.dir/clean

CMakeFiles/fits.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" E:\github\sextractor_win\win E:\github\sextractor_win\win E:\github\sextractor_win\win\build E:\github\sextractor_win\win\build E:\github\sextractor_win\win\build\CMakeFiles\fits.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/fits.dir/depend

