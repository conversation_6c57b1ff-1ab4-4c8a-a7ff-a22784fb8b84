# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\cmake\bin\cmake.exe

# The command to remove a file.
RM = C:\cmake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\github\sextractor_win\win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\github\sextractor_win\win\build

# Include any dependencies generated for this target.
include CMakeFiles/sex.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/sex.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/sex.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/sex.dir/flags.make

CMakeFiles/sex.dir/codegen:
.PHONY : CMakeFiles/sex.dir/codegen

CMakeFiles/sex.dir/E_/github/sextractor_win/src/analyse.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/analyse.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/analyse.c.obj: E:/github/sextractor_win/src/analyse.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/analyse.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/analyse.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/analyse.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\analyse.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\analyse.c.obj -c E:\github\sextractor_win\src\analyse.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/analyse.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/analyse.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\analyse.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\analyse.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/analyse.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/analyse.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\analyse.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\analyse.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/assoc.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/assoc.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/assoc.c.obj: E:/github/sextractor_win/src/assoc.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/assoc.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/assoc.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/assoc.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\assoc.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\assoc.c.obj -c E:\github\sextractor_win\src\assoc.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/assoc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/assoc.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\assoc.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\assoc.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/assoc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/assoc.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\assoc.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\assoc.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/astrom.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/astrom.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/astrom.c.obj: E:/github/sextractor_win/src/astrom.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/astrom.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/astrom.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/astrom.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\astrom.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\astrom.c.obj -c E:\github\sextractor_win\src\astrom.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/astrom.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/astrom.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\astrom.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\astrom.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/astrom.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/astrom.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\astrom.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\astrom.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/back.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/back.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/back.c.obj: E:/github/sextractor_win/src/back.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/back.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/back.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/back.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\back.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\back.c.obj -c E:\github\sextractor_win\src\back.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/back.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/back.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\back.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\back.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/back.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/back.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\back.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\back.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/bpro.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/bpro.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/bpro.c.obj: E:/github/sextractor_win/src/bpro.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/bpro.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/bpro.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/bpro.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\bpro.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\bpro.c.obj -c E:\github\sextractor_win\src\bpro.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/bpro.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/bpro.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\bpro.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\bpro.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/bpro.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/bpro.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\bpro.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\bpro.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/catout.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/catout.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/catout.c.obj: E:/github/sextractor_win/src/catout.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/catout.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/catout.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/catout.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\catout.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\catout.c.obj -c E:\github\sextractor_win\src\catout.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/catout.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/catout.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\catout.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\catout.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/catout.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/catout.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\catout.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\catout.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/check.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/check.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/check.c.obj: E:/github/sextractor_win/src/check.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/check.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/check.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/check.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\check.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\check.c.obj -c E:\github\sextractor_win\src\check.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/check.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/check.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\check.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\check.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/check.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/check.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\check.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\check.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/clean.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/clean.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/clean.c.obj: E:/github/sextractor_win/src/clean.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/clean.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/clean.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/clean.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\clean.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\clean.c.obj -c E:\github\sextractor_win\src\clean.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/clean.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/clean.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\clean.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\clean.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/clean.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/clean.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\clean.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\clean.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/dgeo.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/dgeo.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/dgeo.c.obj: E:/github/sextractor_win/src/dgeo.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/dgeo.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/dgeo.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/dgeo.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\dgeo.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\dgeo.c.obj -c E:\github\sextractor_win\src\dgeo.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/dgeo.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/dgeo.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\dgeo.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\dgeo.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/dgeo.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/dgeo.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\dgeo.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\dgeo.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/extract.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/extract.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/extract.c.obj: E:/github/sextractor_win/src/extract.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/extract.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/extract.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/extract.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\extract.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\extract.c.obj -c E:\github\sextractor_win\src\extract.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/extract.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/extract.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\extract.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\extract.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/extract.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/extract.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\extract.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\extract.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/fft.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/fft.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/fft.c.obj: E:/github/sextractor_win/src/fft.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/fft.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/fft.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/fft.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\fft.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\fft.c.obj -c E:\github\sextractor_win\src\fft.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/fft.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/fft.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\fft.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\fft.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/fft.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/fft.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\fft.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\fft.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/field.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/field.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/field.c.obj: E:/github/sextractor_win/src/field.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/field.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/field.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/field.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\field.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\field.c.obj -c E:\github\sextractor_win\src\field.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/field.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/field.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\field.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\field.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/field.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/field.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\field.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\field.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/filter.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/filter.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/filter.c.obj: E:/github/sextractor_win/src/filter.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/filter.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/filter.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/filter.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\filter.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\filter.c.obj -c E:\github\sextractor_win\src\filter.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/filter.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/filter.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\filter.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\filter.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/filter.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/filter.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\filter.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\filter.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/fitswcs.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/fitswcs.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/fitswcs.c.obj: E:/github/sextractor_win/src/fitswcs.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/fitswcs.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/fitswcs.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/fitswcs.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\fitswcs.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\fitswcs.c.obj -c E:\github\sextractor_win\src\fitswcs.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/fitswcs.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/fitswcs.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\fitswcs.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\fitswcs.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/fitswcs.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/fitswcs.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\fitswcs.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\fitswcs.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/flag.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/flag.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/flag.c.obj: E:/github/sextractor_win/src/flag.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/flag.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/flag.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/flag.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\flag.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\flag.c.obj -c E:\github\sextractor_win\src\flag.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/flag.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/flag.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\flag.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\flag.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/flag.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/flag.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\flag.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\flag.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/graph.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/graph.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/graph.c.obj: E:/github/sextractor_win/src/graph.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/graph.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/graph.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/graph.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\graph.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\graph.c.obj -c E:\github\sextractor_win\src\graph.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/graph.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/graph.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\graph.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\graph.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/graph.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/graph.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\graph.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\graph.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/growth.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/growth.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/growth.c.obj: E:/github/sextractor_win/src/growth.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/growth.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/growth.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/growth.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\growth.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\growth.c.obj -c E:\github\sextractor_win\src\growth.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/growth.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/growth.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\growth.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\growth.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/growth.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/growth.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\growth.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\growth.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/header.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/header.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/header.c.obj: E:/github/sextractor_win/src/header.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/header.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/header.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/header.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\header.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\header.c.obj -c E:\github\sextractor_win\src\header.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/header.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/header.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\header.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\header.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/header.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/header.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\header.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\header.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/image.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/image.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/image.c.obj: E:/github/sextractor_win/src/image.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/image.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/image.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/image.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\image.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\image.c.obj -c E:\github\sextractor_win\src\image.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/image.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/image.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\image.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\image.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/image.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/image.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\image.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\image.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/interpolate.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/interpolate.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/interpolate.c.obj: E:/github/sextractor_win/src/interpolate.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/interpolate.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/interpolate.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/interpolate.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\interpolate.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\interpolate.c.obj -c E:\github\sextractor_win\src\interpolate.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/interpolate.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/interpolate.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\interpolate.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\interpolate.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/interpolate.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/interpolate.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\interpolate.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\interpolate.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/main.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/main.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/main.c.obj: E:/github/sextractor_win/src/main.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/main.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/main.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/main.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\main.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\main.c.obj -c E:\github\sextractor_win\src\main.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/main.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/main.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\main.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\main.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/main.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/main.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\main.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\main.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/makeit.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/makeit.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/makeit.c.obj: E:/github/sextractor_win/src/makeit.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/makeit.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/makeit.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/makeit.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\makeit.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\makeit.c.obj -c E:\github\sextractor_win\src\makeit.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/makeit.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/makeit.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\makeit.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\makeit.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/makeit.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/makeit.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\makeit.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\makeit.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/manobjlist.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/manobjlist.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/manobjlist.c.obj: E:/github/sextractor_win/src/manobjlist.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/manobjlist.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/manobjlist.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/manobjlist.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\manobjlist.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\manobjlist.c.obj -c E:\github\sextractor_win\src\manobjlist.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/manobjlist.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/manobjlist.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\manobjlist.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\manobjlist.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/manobjlist.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/manobjlist.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\manobjlist.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\manobjlist.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/misc.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/misc.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/misc.c.obj: E:/github/sextractor_win/src/misc.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/misc.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/misc.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/misc.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\misc.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\misc.c.obj -c E:\github\sextractor_win\src\misc.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/misc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/misc.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\misc.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\misc.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/misc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/misc.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\misc.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\misc.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/neurro.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/neurro.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/neurro.c.obj: E:/github/sextractor_win/src/neurro.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/neurro.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/neurro.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/neurro.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\neurro.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\neurro.c.obj -c E:\github\sextractor_win\src\neurro.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/neurro.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/neurro.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\neurro.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\neurro.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/neurro.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/neurro.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\neurro.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\neurro.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/pattern.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/pattern.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/pattern.c.obj: E:/github/sextractor_win/src/pattern.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/pattern.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/pattern.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/pattern.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\pattern.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\pattern.c.obj -c E:\github\sextractor_win\src\pattern.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/pattern.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/pattern.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\pattern.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\pattern.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/pattern.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/pattern.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\pattern.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\pattern.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/pc.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/pc.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/pc.c.obj: E:/github/sextractor_win/src/pc.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/pc.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/pc.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/pc.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\pc.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\pc.c.obj -c E:\github\sextractor_win\src\pc.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/pc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/pc.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\pc.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\pc.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/pc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/pc.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\pc.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\pc.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/photom.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/photom.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/photom.c.obj: E:/github/sextractor_win/src/photom.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/photom.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/photom.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/photom.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\photom.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\photom.c.obj -c E:\github\sextractor_win\src\photom.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/photom.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/photom.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\photom.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\photom.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/photom.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/photom.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\photom.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\photom.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/plist.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/plist.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/plist.c.obj: E:/github/sextractor_win/src/plist.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/plist.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/plist.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/plist.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\plist.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\plist.c.obj -c E:\github\sextractor_win\src\plist.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/plist.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/plist.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\plist.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\plist.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/plist.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/plist.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\plist.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\plist.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/prefs.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/prefs.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/prefs.c.obj: E:/github/sextractor_win/src/prefs.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/prefs.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/prefs.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/prefs.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\prefs.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\prefs.c.obj -c E:\github\sextractor_win\src\prefs.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/prefs.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/prefs.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\prefs.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\prefs.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/prefs.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/prefs.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\prefs.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\prefs.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/profit.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/profit.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/profit.c.obj: E:/github/sextractor_win/src/profit.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/profit.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/profit.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/profit.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\profit.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\profit.c.obj -c E:\github\sextractor_win\src\profit.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/profit.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/profit.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\profit.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\profit.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/profit.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/profit.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\profit.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\profit.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/psf.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/psf.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/psf.c.obj: E:/github/sextractor_win/src/psf.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/psf.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/psf.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/psf.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\psf.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\psf.c.obj -c E:\github\sextractor_win\src\psf.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/psf.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/psf.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\psf.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\psf.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/psf.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/psf.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\psf.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\psf.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/readimage.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/readimage.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/readimage.c.obj: E:/github/sextractor_win/src/readimage.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/readimage.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/readimage.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/readimage.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\readimage.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\readimage.c.obj -c E:\github\sextractor_win\src\readimage.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/readimage.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/readimage.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\readimage.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\readimage.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/readimage.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/readimage.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\readimage.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\readimage.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/refine.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/refine.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/refine.c.obj: E:/github/sextractor_win/src/refine.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/refine.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/refine.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/refine.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\refine.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\refine.c.obj -c E:\github\sextractor_win\src\refine.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/refine.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/refine.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\refine.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\refine.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/refine.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/refine.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\refine.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\refine.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/retina.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/retina.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/retina.c.obj: E:/github/sextractor_win/src/retina.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/retina.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/retina.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/retina.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\retina.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\retina.c.obj -c E:\github\sextractor_win\src\retina.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/retina.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/retina.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\retina.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\retina.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/retina.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/retina.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\retina.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\retina.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/scan.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/scan.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/scan.c.obj: E:/github/sextractor_win/src/scan.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/scan.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_36) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/scan.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/scan.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\scan.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\scan.c.obj -c E:\github\sextractor_win\src\scan.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/scan.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/scan.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\scan.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\scan.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/scan.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/scan.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\scan.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\scan.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/som.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/som.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/som.c.obj: E:/github/sextractor_win/src/som.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/som.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_37) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/som.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/som.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\som.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\som.c.obj -c E:\github\sextractor_win\src\som.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/som.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/som.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\som.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\som.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/som.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/som.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\som.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\som.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/weight.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/weight.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/weight.c.obj: E:/github/sextractor_win/src/weight.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/weight.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_38) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/weight.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/weight.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\weight.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\weight.c.obj -c E:\github\sextractor_win\src\weight.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/weight.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/weight.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\weight.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\weight.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/weight.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/weight.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\weight.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\weight.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/winpos.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/winpos.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/winpos.c.obj: E:/github/sextractor_win/src/winpos.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/winpos.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_39) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/winpos.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/winpos.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\winpos.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\winpos.c.obj -c E:\github\sextractor_win\src\winpos.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/winpos.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/winpos.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\winpos.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\winpos.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/winpos.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/winpos.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\winpos.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\winpos.c.s

CMakeFiles/sex.dir/E_/github/sextractor_win/src/xml.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/E_/github/sextractor_win/src/xml.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/E_/github/sextractor_win/src/xml.c.obj: E:/github/sextractor_win/src/xml.c
CMakeFiles/sex.dir/E_/github/sextractor_win/src/xml.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_40) "Building C object CMakeFiles/sex.dir/E_/github/sextractor_win/src/xml.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/E_/github/sextractor_win/src/xml.c.obj -MF CMakeFiles\sex.dir\E_\github\sextractor_win\src\xml.c.obj.d -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\xml.c.obj -c E:\github\sextractor_win\src\xml.c

CMakeFiles/sex.dir/E_/github/sextractor_win/src/xml.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/E_/github/sextractor_win/src/xml.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\src\xml.c > CMakeFiles\sex.dir\E_\github\sextractor_win\src\xml.c.i

CMakeFiles/sex.dir/E_/github/sextractor_win/src/xml.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/E_/github/sextractor_win/src/xml.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\src\xml.c -o CMakeFiles\sex.dir\E_\github\sextractor_win\src\xml.c.s

CMakeFiles/sex.dir/windows_stubs.c.obj: CMakeFiles/sex.dir/flags.make
CMakeFiles/sex.dir/windows_stubs.c.obj: CMakeFiles/sex.dir/includes_C.rsp
CMakeFiles/sex.dir/windows_stubs.c.obj: E:/github/sextractor_win/win/windows_stubs.c
CMakeFiles/sex.dir/windows_stubs.c.obj: CMakeFiles/sex.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_41) "Building C object CMakeFiles/sex.dir/windows_stubs.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/sex.dir/windows_stubs.c.obj -MF CMakeFiles\sex.dir\windows_stubs.c.obj.d -o CMakeFiles\sex.dir\windows_stubs.c.obj -c E:\github\sextractor_win\win\windows_stubs.c

CMakeFiles/sex.dir/windows_stubs.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/sex.dir/windows_stubs.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E E:\github\sextractor_win\win\windows_stubs.c > CMakeFiles\sex.dir\windows_stubs.c.i

CMakeFiles/sex.dir/windows_stubs.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/sex.dir/windows_stubs.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S E:\github\sextractor_win\win\windows_stubs.c -o CMakeFiles\sex.dir\windows_stubs.c.s

# Object files for target sex
sex_OBJECTS = \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/analyse.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/assoc.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/astrom.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/back.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/bpro.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/catout.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/check.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/clean.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/dgeo.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/extract.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/fft.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/field.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/filter.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/fitswcs.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/flag.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/graph.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/growth.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/header.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/image.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/interpolate.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/main.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/makeit.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/manobjlist.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/misc.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/neurro.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/pattern.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/pc.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/photom.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/plist.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/prefs.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/profit.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/psf.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/readimage.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/refine.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/retina.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/scan.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/som.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/weight.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/winpos.c.obj" \
"CMakeFiles/sex.dir/E_/github/sextractor_win/src/xml.c.obj" \
"CMakeFiles/sex.dir/windows_stubs.c.obj"

# External object files for target sex
sex_EXTERNAL_OBJECTS =

sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/analyse.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/assoc.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/astrom.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/back.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/bpro.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/catout.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/check.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/clean.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/dgeo.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/extract.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/fft.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/field.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/filter.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/fitswcs.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/flag.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/graph.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/growth.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/header.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/image.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/interpolate.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/main.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/makeit.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/manobjlist.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/misc.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/neurro.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/pattern.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/pc.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/photom.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/plist.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/prefs.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/profit.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/psf.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/readimage.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/refine.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/retina.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/scan.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/som.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/weight.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/winpos.c.obj
sex.exe: CMakeFiles/sex.dir/E_/github/sextractor_win/src/xml.c.obj
sex.exe: CMakeFiles/sex.dir/windows_stubs.c.obj
sex.exe: CMakeFiles/sex.dir/build.make
sex.exe: libfits.a
sex.exe: libwcs.a
sex.exe: liblevmar.a
sex.exe: C:/msys64/mingw64/lib/libfftw3f.dll.a
sex.exe: C:/msys64/mingw64/lib/libcfitsio.dll.a
sex.exe: C:/msys64/mingw64/lib/liblapack.dll.a
sex.exe: C:/msys64/mingw64/lib/libopenblas.dll.a
sex.exe: CMakeFiles/sex.dir/linkLibs.rsp
sex.exe: CMakeFiles/sex.dir/objects1.rsp
sex.exe: CMakeFiles/sex.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=E:\github\sextractor_win\win\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_42) "Linking C executable sex.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\sex.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/sex.dir/build: sex.exe
.PHONY : CMakeFiles/sex.dir/build

CMakeFiles/sex.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\sex.dir\cmake_clean.cmake
.PHONY : CMakeFiles/sex.dir/clean

CMakeFiles/sex.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" E:\github\sextractor_win\win E:\github\sextractor_win\win E:\github\sextractor_win\win\build E:\github\sextractor_win\win\build E:\github\sextractor_win\win\build\CMakeFiles\sex.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/sex.dir/depend

