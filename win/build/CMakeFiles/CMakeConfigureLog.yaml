
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: C:/msys64/mingw64/bin/cc.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.exe"
      
      The C compiler identification is GNU, found in:
        E:/github/sextractor_win/win/build/CMakeFiles/3.31.8/CompilerIdC/a.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "E:/github/sextractor_win/win/build/CMakeFiles/CMakeScratch/TryCompile-dh9ebj"
      binary: "E:/github/sextractor_win/win/build/CMakeFiles/CMakeScratch/TryCompile-dh9ebj"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'E:/github/sextractor_win/win/build/CMakeFiles/CMakeScratch/TryCompile-dh9ebj'
        
        Run Build Command(s): C:/cmake/bin/cmake.exe -E env VERBOSE=1 C:/msys64/mingw64/bin/mingw32-make.exe -f Makefile cmTC_6b7c7/fast
        C:/msys64/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_6b7c7.dir\\build.make CMakeFiles/cmTC_6b7c7.dir/build
        mingw32-make[1]: Entering directory 'E:/github/sextractor_win/win/build/CMakeFiles/CMakeScratch/TryCompile-dh9ebj'
        Building C object CMakeFiles/cmTC_6b7c7.dir/CMakeCCompilerABI.c.obj
        C:\\msys64\\mingw64\\bin\\cc.exe   -v -o CMakeFiles\\cmTC_6b7c7.dir\\CMakeCCompilerABI.c.obj -c C:\\cmake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c
        Using built-in specs.
        COLLECT_GCC=C:\\msys64\\mingw64\\bin\\cc.exe
        Target: x86_64-w64-mingw32
        Configured with: ../gcc-15.2.0/configure --prefix=/mingw64 --with-local-prefix=/mingw64/local --with-native-system-header-dir=/mingw64/include --libexecdir=/mingw64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-mingw-wildcard --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-backtrace=yes --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/mingw64 --with-mpfr=/mingw64 --with-mpc=/mingw64 --with-isl=/mingw64 --with-pkgversion='Rev8, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --with-libstdcxx-zoneinfo=yes --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 15.2.0 (Rev8, Built by MSYS2 project) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_6b7c7.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_6b7c7.dir\\'
         C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/cc1.exe -quiet -v -iprefix C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/ -D_REENTRANT C:\\cmake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles\\cmTC_6b7c7.dir\\ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=nocona -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc3xbbXl.s
        GNU C23 (Rev8, Built by MSYS2 project) version 15.2.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 15.2.0, GMP version 6.3.0, MPFR version 4.2.2, MPC version 1.3.1, isl version isl-0.27-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring nonexistent directory "C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/include"
        ignoring duplicate directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/include"
        ignoring nonexistent directory "D:/M/msys64/mingw64/include"
        ignoring nonexistent directory "/mingw64/include"
        ignoring duplicate directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed"
        ignoring nonexistent directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/include"
        ignoring nonexistent directory "D:/M/msys64/mingw64/include"
        #include "..." search starts here:
        #include <...> search starts here:
         C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include
         C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../include
         C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed
        End of search list.
        Compiler executable checksum: feaaa8b8d0ead308dc62d839201f7330
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_6b7c7.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_6b7c7.dir\\'
         C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_6b7c7.dir\\CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc3xbbXl.s
        GNU assembler version 2.45 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.45
        COMPILER_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/;C:/msys64/mingw64/bin/../lib/gcc/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/;C:/msys64/mingw64/bin/../lib/gcc/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_6b7c7.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_6b7c7.dir\\CMakeCCompilerABI.c.'
        Linking C executable cmTC_6b7c7.exe
        C:\\cmake\\bin\\cmake.exe -E cmake_link_script CMakeFiles\\cmTC_6b7c7.dir\\link.txt --verbose=1
        C:\\cmake\\bin\\cmake.exe -E rm -f CMakeFiles\\cmTC_6b7c7.dir/objects.a
        C:\\msys64\\mingw64\\bin\\ar.exe qc CMakeFiles\\cmTC_6b7c7.dir/objects.a @CMakeFiles\\cmTC_6b7c7.dir\\objects1.rsp
        C:\\msys64\\mingw64\\bin\\cc.exe  -v -Wl,-v -Wl,--whole-archive CMakeFiles\\cmTC_6b7c7.dir/objects.a -Wl,--no-whole-archive -o cmTC_6b7c7.exe -Wl,--out-implib,libcmTC_6b7c7.dll.a -Wl,--major-image-version,0,--minor-image-version,0
        Using built-in specs.
        COLLECT_GCC=C:\\msys64\\mingw64\\bin\\cc.exe
        COLLECT_LTO_WRAPPER=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: ../gcc-15.2.0/configure --prefix=/mingw64 --with-local-prefix=/mingw64/local --with-native-system-header-dir=/mingw64/include --libexecdir=/mingw64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-mingw-wildcard --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-backtrace=yes --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/mingw64 --with-mpfr=/mingw64 --with-mpc=/mingw64 --with-isl=/mingw64 --with-pkgversion='Rev8, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --with-libstdcxx-zoneinfo=yes --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 15.2.0 (Rev8, Built by MSYS2 project) 
        COMPILER_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/;C:/msys64/mingw64/bin/../lib/gcc/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/;C:/msys64/mingw64/bin/../lib/gcc/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_6b7c7.exe' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_6b7c7.'
         C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/collect2.exe -plugin C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/liblto_plugin.dll -plugin-opt=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccL4gPBJ.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_6b7c7.exe C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/crt2.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0 -LC:/msys64/mingw64/bin/../lib/gcc -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../.. -v --whole-archive CMakeFiles\\cmTC_6b7c7.dir/objects.a --no-whole-archive --out-implib libcmTC_6b7c7.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/default-manifest.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o
        collect2 version 15.2.0
        C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/liblto_plugin.dll -plugin-opt=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccL4gPBJ.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_6b7c7.exe C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/crt2.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0 -LC:/msys64/mingw64/bin/../lib/gcc -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../.. -v --whole-archive CMakeFiles\\cmTC_6b7c7.dir/objects.a --no-whole-archive --out-implib libcmTC_6b7c7.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/default-manifest.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o
        GNU ld (GNU Binutils) 2.45
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_6b7c7.exe' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_6b7c7.'
        mingw32-make[1]: Leaving directory 'E:/github/sextractor_win/win/build/CMakeFiles/CMakeScratch/TryCompile-dh9ebj'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:182 (message)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include]
          add: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../include]
          add: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed]
        end of search list found
        collapse include dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include] ==> [C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include]
        collapse include dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../include] ==> [C:/msys64/mingw64/include]
        collapse include dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed] ==> [C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed]
        implicit include dirs: [C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include;C:/msys64/mingw64/include;C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?))("|,| |$)]
        ignore line: [Change Dir: 'E:/github/sextractor_win/win/build/CMakeFiles/CMakeScratch/TryCompile-dh9ebj']
        ignore line: []
        ignore line: [Run Build Command(s): C:/cmake/bin/cmake.exe -E env VERBOSE=1 C:/msys64/mingw64/bin/mingw32-make.exe -f Makefile cmTC_6b7c7/fast]
        ignore line: [C:/msys64/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_6b7c7.dir\\build.make CMakeFiles/cmTC_6b7c7.dir/build]
        ignore line: [mingw32-make[1]: Entering directory 'E:/github/sextractor_win/win/build/CMakeFiles/CMakeScratch/TryCompile-dh9ebj']
        ignore line: [Building C object CMakeFiles/cmTC_6b7c7.dir/CMakeCCompilerABI.c.obj]
        ignore line: [C:\\msys64\\mingw64\\bin\\cc.exe   -v -o CMakeFiles\\cmTC_6b7c7.dir\\CMakeCCompilerABI.c.obj -c C:\\cmake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\msys64\\mingw64\\bin\\cc.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../gcc-15.2.0/configure --prefix=/mingw64 --with-local-prefix=/mingw64/local --with-native-system-header-dir=/mingw64/include --libexecdir=/mingw64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-mingw-wildcard --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-backtrace=yes --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/mingw64 --with-mpfr=/mingw64 --with-mpc=/mingw64 --with-isl=/mingw64 --with-pkgversion='Rev8, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --with-libstdcxx-zoneinfo=yes --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 15.2.0 (Rev8  Built by MSYS2 project) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_6b7c7.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_6b7c7.dir\\']
        ignore line: [ C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/cc1.exe -quiet -v -iprefix C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/ -D_REENTRANT C:\\cmake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles\\cmTC_6b7c7.dir\\ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=nocona -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc3xbbXl.s]
        ignore line: [GNU C23 (Rev8  Built by MSYS2 project) version 15.2.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 15.2.0  GMP version 6.3.0  MPFR version 4.2.2  MPC version 1.3.1  isl version isl-0.27-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring nonexistent directory "C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring duplicate directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/include"]
        ignore line: [ignoring nonexistent directory "D:/M/msys64/mingw64/include"]
        ignore line: [ignoring nonexistent directory "/mingw64/include"]
        ignore line: [ignoring duplicate directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed"]
        ignore line: [ignoring nonexistent directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring nonexistent directory "D:/M/msys64/mingw64/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include]
        ignore line: [ C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../include]
        ignore line: [ C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: feaaa8b8d0ead308dc62d839201f7330]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_6b7c7.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_6b7c7.dir\\']
        ignore line: [ C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_6b7c7.dir\\CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cc3xbbXl.s]
        ignore line: [GNU assembler version 2.45 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.45]
        ignore line: [COMPILER_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_6b7c7.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_6b7c7.dir\\CMakeCCompilerABI.c.']
        ignore line: [Linking C executable cmTC_6b7c7.exe]
        ignore line: [C:\\cmake\\bin\\cmake.exe -E cmake_link_script CMakeFiles\\cmTC_6b7c7.dir\\link.txt --verbose=1]
        ignore line: [C:\\cmake\\bin\\cmake.exe -E rm -f CMakeFiles\\cmTC_6b7c7.dir/objects.a]
        ignore line: [C:\\msys64\\mingw64\\bin\\ar.exe qc CMakeFiles\\cmTC_6b7c7.dir/objects.a @CMakeFiles\\cmTC_6b7c7.dir\\objects1.rsp]
        ignore line: [C:\\msys64\\mingw64\\bin\\cc.exe  -v -Wl -v -Wl --whole-archive CMakeFiles\\cmTC_6b7c7.dir/objects.a -Wl --no-whole-archive -o cmTC_6b7c7.exe -Wl --out-implib libcmTC_6b7c7.dll.a -Wl --major-image-version 0 --minor-image-version 0]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\msys64\\mingw64\\bin\\cc.exe]
        ignore line: [COLLECT_LTO_WRAPPER=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../gcc-15.2.0/configure --prefix=/mingw64 --with-local-prefix=/mingw64/local --with-native-system-header-dir=/mingw64/include --libexecdir=/mingw64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-mingw-wildcard --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-backtrace=yes --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/mingw64 --with-mpfr=/mingw64 --with-mpc=/mingw64 --with-isl=/mingw64 --with-pkgversion='Rev8, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --with-libstdcxx-zoneinfo=yes --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 15.2.0 (Rev8  Built by MSYS2 project) ]
        ignore line: [COMPILER_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_6b7c7.exe' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_6b7c7.']
        link line: [ C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/collect2.exe -plugin C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/liblto_plugin.dll -plugin-opt=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccL4gPBJ.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_6b7c7.exe C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/crt2.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0 -LC:/msys64/mingw64/bin/../lib/gcc -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../.. -v --whole-archive CMakeFiles\\cmTC_6b7c7.dir/objects.a --no-whole-archive --out-implib libcmTC_6b7c7.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/default-manifest.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o]
          arg [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/liblto_plugin.dll] ==> ignore
          arg [-plugin-opt=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccL4gPBJ.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_6b7c7.exe] ==> ignore
          arg [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/crt2.o] ==> obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/crt2.o]
          arg [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o] ==> obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o]
          arg [-LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0] ==> dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0]
          arg [-LC:/msys64/mingw64/bin/../lib/gcc] ==> dir [C:/msys64/mingw64/bin/../lib/gcc]
          arg [-LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib] ==> dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib]
          arg [-LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib] ==> dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../..] ==> dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../..]
          arg [-v] ==> ignore
          arg [--whole-archive] ==> ignore
          arg [CMakeFiles\\cmTC_6b7c7.dir/objects.a] ==> ignore
          arg [--no-whole-archive] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_6b7c7.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lpthread] ==> lib [pthread]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/default-manifest.o] ==> obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/default-manifest.o]
          arg [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o] ==> obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o]
        ignore line: [collect2 version 15.2.0]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/liblto_plugin.dll -plugin-opt=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccL4gPBJ.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_6b7c7.exe C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/crt2.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0 -LC:/msys64/mingw64/bin/../lib/gcc -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../.. -v --whole-archive CMakeFiles\\cmTC_6b7c7.dir/objects.a --no-whole-archive --out-implib libcmTC_6b7c7.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/default-manifest.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o]
        linker tool for 'C': C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe
        remove lib [gcc_eh]
        remove lib [msvcrt]
        remove lib [gcc_eh]
        remove lib [msvcrt]
        collapse obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/crt2.o] ==> [C:/msys64/mingw64/lib/crt2.o]
        collapse obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o] ==> [C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o]
        collapse obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/default-manifest.o] ==> [C:/msys64/mingw64/lib/default-manifest.o]
        collapse obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o] ==> [C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o]
        collapse library dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0] ==> [C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0]
        collapse library dir [C:/msys64/mingw64/bin/../lib/gcc] ==> [C:/msys64/mingw64/lib/gcc]
        collapse library dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [C:/msys64/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib] ==> [C:/msys64/mingw64/lib]
        collapse library dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib] ==> [C:/msys64/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../..] ==> [C:/msys64/mingw64/lib]
        implicit libs: [mingw32;gcc;mingwex;kernel32;pthread;advapi32;shell32;user32;kernel32;mingw32;gcc;mingwex;kernel32]
        implicit objs: [C:/msys64/mingw64/lib/crt2.o;C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o;C:/msys64/mingw64/lib/default-manifest.o;C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o]
        implicit dirs: [C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0;C:/msys64/mingw64/lib/gcc;C:/msys64/mingw64/x86_64-w64-mingw32/lib;C:/msys64/mingw64/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/cmake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/cmake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe" "-v"
      GNU ld (GNU Binutils) 2.45
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompiler.cmake:115 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:67 (_cmake_find_compiler_path)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_C_COMPILER_WITH_PATH"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc"
    candidate_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/cmake/bin/"
      - "E:/miniconda3/condabin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/libnvvp/"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/mayong/jdk1.8.0_101/bin/"
      - "C:/gnu/bin___/"
      - "C:/Program Files/Git/cmd/"
      - "C:/msys64/mingw64/bin__/"
      - "E:/github/sextractor_win/win/build/%JAA_HOME%/bin/"
      - "C:/PuTTY/"
      - "C:/Program Files/TortoiseSVN/bin/"
      - "C:/Python/Python310/"
      - "C:/Python/Python310/Scripts/"
      - "C:/Program Files/dotnet/"
      - "C:/gnu/bin/"
      - "C:/Program Files/TortoiseGit/bin/"
      - "E:/nodejs/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "E:/ffmpeg/bin/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2022.3.0/"
      - "E:/miniconda3/Scripts/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/python36/Scripts/"
      - "C:/python36/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/"
      - "C:/Python/Python310/_/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Python/Python310/Scripts/___/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/Programs/MiKTeX/miktex/bin/x64/"
      - "E:/Lingma/bin/"
      - "E:/Microsoft VS Code/bin/"
    searched_directories:
      - "C:/msys64/mingw64/bin/gcc.com"
    found: "C:/msys64/mingw64/bin/gcc.exe"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "../install"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "../install"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: C:/msys64/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.exe"
      
      The C compiler identification is GNU, found in:
        E:/github/sextractor_win/win/build/CMakeFiles/4.1.0/CompilerIdC/a.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "E:/github/sextractor_win/win/build/CMakeFiles/CMakeScratch/TryCompile-4baqp3"
      binary: "E:/github/sextractor_win/win/build/CMakeFiles/CMakeScratch/TryCompile-4baqp3"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'E:/github/sextractor_win/win/build/CMakeFiles/CMakeScratch/TryCompile-4baqp3'
        
        Run Build Command(s): C:/msys64/mingw64/bin/cmake.exe -E env VERBOSE=1 C:/msys64/mingw64/bin/mingw32-make.exe -f Makefile cmTC_a3999/fast
        C:/msys64/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_a3999.dir\\build.make CMakeFiles/cmTC_a3999.dir/build
        mingw32-make[1]: Entering directory 'E:/github/sextractor_win/win/build/CMakeFiles/CMakeScratch/TryCompile-4baqp3'
        Building C object CMakeFiles/cmTC_a3999.dir/CMakeCCompilerABI.c.obj
        C:\\msys64\\mingw64\\bin\\gcc.exe   -v -o CMakeFiles\\cmTC_a3999.dir\\CMakeCCompilerABI.c.obj -c C:\\msys64\\mingw64\\share\\cmake\\Modules\\CMakeCCompilerABI.c
        Using built-in specs.
        COLLECT_GCC=C:\\msys64\\mingw64\\bin\\gcc.exe
        Target: x86_64-w64-mingw32
        Configured with: ../gcc-15.2.0/configure --prefix=/mingw64 --with-local-prefix=/mingw64/local --with-native-system-header-dir=/mingw64/include --libexecdir=/mingw64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-mingw-wildcard --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-backtrace=yes --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/mingw64 --with-mpfr=/mingw64 --with-mpc=/mingw64 --with-isl=/mingw64 --with-pkgversion='Rev8, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --with-libstdcxx-zoneinfo=yes --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 15.2.0 (Rev8, Built by MSYS2 project) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_a3999.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_a3999.dir\\'
         C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/cc1.exe -quiet -v -iprefix C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/ -D_REENTRANT C:\\msys64\\mingw64\\share\\cmake\\Modules\\CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles\\cmTC_a3999.dir\\ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=nocona -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cchajiR2.s
        GNU C23 (Rev8, Built by MSYS2 project) version 15.2.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 15.2.0, GMP version 6.3.0, MPFR version 4.2.2, MPC version 1.3.1, isl version isl-0.27-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring nonexistent directory "C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/include"
        ignoring duplicate directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/include"
        ignoring nonexistent directory "D:/M/msys64/mingw64/include"
        ignoring nonexistent directory "/mingw64/include"
        ignoring duplicate directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed"
        ignoring nonexistent directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/include"
        ignoring nonexistent directory "D:/M/msys64/mingw64/include"
        #include "..." search starts here:
        #include <...> search starts here:
         C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include
         C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../include
         C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed
        End of search list.
        Compiler executable checksum: feaaa8b8d0ead308dc62d839201f7330
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_a3999.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_a3999.dir\\'
         C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_a3999.dir\\CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cchajiR2.s
        GNU assembler version 2.45 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.45
        COMPILER_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/;C:/msys64/mingw64/bin/../lib/gcc/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/;C:/msys64/mingw64/bin/../lib/gcc/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_a3999.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_a3999.dir\\CMakeCCompilerABI.c.'
        Linking C executable cmTC_a3999.exe
        C:\\msys64\\mingw64\\bin\\cmake.exe -E cmake_link_script CMakeFiles\\cmTC_a3999.dir\\link.txt --verbose=1
        Using built-in specs.
        COLLECT_GCC=C:\\msys64\\mingw64\\bin\\gcc.exe
        COLLECT_LTO_WRAPPER=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: ../gcc-15.2.0/configure --prefix=/mingw64 --with-local-prefix=/mingw64/local --with-native-system-header-dir=/mingw64/include --libexecdir=/mingw64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-mingw-wildcard --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-backtrace=yes --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/mingw64 --with-mpfr=/mingw64 --with-mpc=/mingw64 --with-isl=/mingw64 --with-pkgversion='Rev8, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --with-libstdcxx-zoneinfo=yes --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 15.2.0 (Rev8, Built by MSYS2 project) 
        COMPILER_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/;C:/msys64/mingw64/bin/../lib/gcc/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/;C:/msys64/mingw64/bin/../lib/gcc/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_a3999.exe' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_a3999.'
         C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/collect2.exe -plugin C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/liblto_plugin.dll -plugin-opt=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccUptUph.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_a3999.exe C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/crt2.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0 -LC:/msys64/mingw64/bin/../lib/gcc -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../.. -v --whole-archive CMakeFiles\\cmTC_a3999.dir/objects.a --no-whole-archive --out-implib libcmTC_a3999.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/default-manifest.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o
        collect2 version 15.2.0
        C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/liblto_plugin.dll -plugin-opt=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccUptUph.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_a3999.exe C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/crt2.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0 -LC:/msys64/mingw64/bin/../lib/gcc -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../.. -v --whole-archive CMakeFiles\\cmTC_a3999.dir/objects.a --no-whole-archive --out-implib libcmTC_a3999.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/default-manifest.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o
        GNU ld (GNU Binutils) 2.45
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_a3999.exe' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_a3999.'
        C:\\msys64\\mingw64\\bin\\cmake.exe -E rm -f CMakeFiles\\cmTC_a3999.dir/objects.a
        C:\\msys64\\mingw64\\bin\\ar.exe qc CMakeFiles\\cmTC_a3999.dir/objects.a @CMakeFiles\\cmTC_a3999.dir\\objects1.rsp
        C:\\msys64\\mingw64\\bin\\gcc.exe  -v -Wl,-v -Wl,--whole-archive CMakeFiles\\cmTC_a3999.dir/objects.a -Wl,--no-whole-archive -o cmTC_a3999.exe -Wl,--out-implib,libcmTC_a3999.dll.a -Wl,--major-image-version,0,--minor-image-version,0
        mingw32-make[1]: Leaving directory 'E:/github/sextractor_win/win/build/CMakeFiles/CMakeScratch/TryCompile-4baqp3'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:217 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include]
          add: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../include]
          add: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed]
        end of search list found
        collapse include dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include] ==> [C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include]
        collapse include dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../include] ==> [C:/msys64/mingw64/include]
        collapse include dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed] ==> [C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed]
        implicit include dirs: [C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include;C:/msys64/mingw64/include;C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: 'E:/github/sextractor_win/win/build/CMakeFiles/CMakeScratch/TryCompile-4baqp3']
        ignore line: []
        ignore line: [Run Build Command(s): C:/msys64/mingw64/bin/cmake.exe -E env VERBOSE=1 C:/msys64/mingw64/bin/mingw32-make.exe -f Makefile cmTC_a3999/fast]
        ignore line: [C:/msys64/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_a3999.dir\\build.make CMakeFiles/cmTC_a3999.dir/build]
        ignore line: [mingw32-make[1]: Entering directory 'E:/github/sextractor_win/win/build/CMakeFiles/CMakeScratch/TryCompile-4baqp3']
        ignore line: [Building C object CMakeFiles/cmTC_a3999.dir/CMakeCCompilerABI.c.obj]
        ignore line: [C:\\msys64\\mingw64\\bin\\gcc.exe   -v -o CMakeFiles\\cmTC_a3999.dir\\CMakeCCompilerABI.c.obj -c C:\\msys64\\mingw64\\share\\cmake\\Modules\\CMakeCCompilerABI.c]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\msys64\\mingw64\\bin\\gcc.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../gcc-15.2.0/configure --prefix=/mingw64 --with-local-prefix=/mingw64/local --with-native-system-header-dir=/mingw64/include --libexecdir=/mingw64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-mingw-wildcard --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-backtrace=yes --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/mingw64 --with-mpfr=/mingw64 --with-mpc=/mingw64 --with-isl=/mingw64 --with-pkgversion='Rev8, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --with-libstdcxx-zoneinfo=yes --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 15.2.0 (Rev8  Built by MSYS2 project) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_a3999.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_a3999.dir\\']
        ignore line: [ C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/cc1.exe -quiet -v -iprefix C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/ -D_REENTRANT C:\\msys64\\mingw64\\share\\cmake\\Modules\\CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles\\cmTC_a3999.dir\\ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=generic -march=nocona -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\cchajiR2.s]
        ignore line: [GNU C23 (Rev8  Built by MSYS2 project) version 15.2.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 15.2.0  GMP version 6.3.0  MPFR version 4.2.2  MPC version 1.3.1  isl version isl-0.27-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring nonexistent directory "C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring duplicate directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/include"]
        ignore line: [ignoring nonexistent directory "D:/M/msys64/mingw64/include"]
        ignore line: [ignoring nonexistent directory "/mingw64/include"]
        ignore line: [ignoring duplicate directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed"]
        ignore line: [ignoring nonexistent directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring nonexistent directory "D:/M/msys64/mingw64/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include]
        ignore line: [ C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../include]
        ignore line: [ C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/include-fixed]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: feaaa8b8d0ead308dc62d839201f7330]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_a3999.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_a3999.dir\\']
        ignore line: [ C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_a3999.dir\\CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\cchajiR2.s]
        ignore line: [GNU assembler version 2.45 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.45]
        ignore line: [COMPILER_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_a3999.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_a3999.dir\\CMakeCCompilerABI.c.']
        ignore line: [Linking C executable cmTC_a3999.exe]
        ignore line: [C:\\msys64\\mingw64\\bin\\cmake.exe -E cmake_link_script CMakeFiles\\cmTC_a3999.dir\\link.txt --verbose=1]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\msys64\\mingw64\\bin\\gcc.exe]
        ignore line: [COLLECT_LTO_WRAPPER=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../gcc-15.2.0/configure --prefix=/mingw64 --with-local-prefix=/mingw64/local --with-native-system-header-dir=/mingw64/include --libexecdir=/mingw64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-mingw-wildcard --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-backtrace=yes --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/mingw64 --with-mpfr=/mingw64 --with-mpc=/mingw64 --with-isl=/mingw64 --with-pkgversion='Rev8, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --with-libstdcxx-zoneinfo=yes --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 15.2.0 (Rev8  Built by MSYS2 project) ]
        ignore line: [COMPILER_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_a3999.exe' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_a3999.']
        link line: [ C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/collect2.exe -plugin C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/liblto_plugin.dll -plugin-opt=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccUptUph.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_a3999.exe C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/crt2.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0 -LC:/msys64/mingw64/bin/../lib/gcc -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../.. -v --whole-archive CMakeFiles\\cmTC_a3999.dir/objects.a --no-whole-archive --out-implib libcmTC_a3999.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/default-manifest.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o]
          arg [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/liblto_plugin.dll] ==> ignore
          arg [-plugin-opt=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccUptUph.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_a3999.exe] ==> ignore
          arg [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/crt2.o] ==> obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/crt2.o]
          arg [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o] ==> obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o]
          arg [-LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0] ==> dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0]
          arg [-LC:/msys64/mingw64/bin/../lib/gcc] ==> dir [C:/msys64/mingw64/bin/../lib/gcc]
          arg [-LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib] ==> dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib]
          arg [-LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib] ==> dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../..] ==> dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../..]
          arg [-v] ==> ignore
          arg [--whole-archive] ==> ignore
          arg [CMakeFiles\\cmTC_a3999.dir/objects.a] ==> ignore
          arg [--no-whole-archive] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_a3999.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lpthread] ==> lib [pthread]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/default-manifest.o] ==> obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/default-manifest.o]
          arg [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o] ==> obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o]
        ignore line: [collect2 version 15.2.0]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/liblto_plugin.dll -plugin-opt=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccUptUph.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bdynamic -o cmTC_a3999.exe C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/crt2.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0 -LC:/msys64/mingw64/bin/../lib/gcc -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../.. -v --whole-archive CMakeFiles\\cmTC_a3999.dir/objects.a --no-whole-archive --out-implib libcmTC_a3999.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/default-manifest.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o]
        linker tool for 'C': C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe
        remove lib [gcc_eh]
        remove lib [msvcrt]
        remove lib [gcc_eh]
        remove lib [msvcrt]
        collapse obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/crt2.o] ==> [C:/msys64/mingw64/lib/crt2.o]
        collapse obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o] ==> [C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o]
        collapse obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib/default-manifest.o] ==> [C:/msys64/mingw64/lib/default-manifest.o]
        collapse obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o] ==> [C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o]
        collapse library dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0] ==> [C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0]
        collapse library dir [C:/msys64/mingw64/bin/../lib/gcc] ==> [C:/msys64/mingw64/lib/gcc]
        collapse library dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [C:/msys64/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../lib] ==> [C:/msys64/mingw64/lib]
        collapse library dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../../../x86_64-w64-mingw32/lib] ==> [C:/msys64/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.2.0/../../..] ==> [C:/msys64/mingw64/lib]
        implicit libs: [mingw32;gcc;mingwex;kernel32;pthread;advapi32;shell32;user32;kernel32;mingw32;gcc;mingwex;kernel32]
        implicit objs: [C:/msys64/mingw64/lib/crt2.o;C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/crtbegin.o;C:/msys64/mingw64/lib/default-manifest.o;C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0/crtend.o]
        implicit dirs: [C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.2.0;C:/msys64/mingw64/lib/gcc;C:/msys64/mingw64/x86_64-w64-mingw32/lib;C:/msys64/mingw64/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe" "-v"
      GNU ld (GNU Binutils) 2.45
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:32 (find_library)"
    mode: "library"
    variable: "MATH_LIBRARY"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "m"
    candidate_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/cmake/bin/"
      - "E:/miniconda3/condabin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/libnvvp/"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/mayong/jdk1.8.0_101/bin/"
      - "C:/gnu/bin___/"
      - "C:/Program Files/Git/cmd/"
      - "C:/msys64/mingw64/bin__/"
      - "E:/github/sextractor_win/win/build/%JAA_HOME%/bin/"
      - "C:/PuTTY/"
      - "C:/Program Files/TortoiseSVN/bin/"
      - "C:/Python/Python310/"
      - "C:/Python/Python310/Scripts/"
      - "C:/Program Files/dotnet/"
      - "C:/gnu/bin/"
      - "C:/Program Files/TortoiseGit/bin/"
      - "E:/nodejs/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "E:/ffmpeg/bin/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2022.3.0/"
      - "E:/miniconda3/Scripts/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/python36/Scripts/"
      - "C:/python36/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/"
      - "C:/Python/Python310/_/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Python/Python310/Scripts/___/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/Programs/MiKTeX/miktex/bin/x64/"
      - "E:/Lingma/bin/"
      - "E:/Microsoft VS Code/bin/"
      - "C:/Program Files/lib/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/lib/"
      - "C:/Program Files (x86)/"
      - "C:/msys64/mingw64/lib/"
      - "C:/msys64/mingw64/"
      - "E:/github/sextractor_win/win/install/lib/"
      - "E:/github/sextractor_win/win/install/"
      - "E:/github/sextractor_win/win/install/bin/"
      - "/bin/"
    searched_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/cmake/bin/"
      - "E:/miniconda3/condabin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/libnvvp/"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/mayong/jdk1.8.0_101/bin/"
      - "C:/gnu/bin___/"
      - "C:/Program Files/Git/cmd/"
      - "C:/msys64/mingw64/bin__/"
      - "E:/github/sextractor_win/win/build/%JAA_HOME%/bin/"
      - "C:/PuTTY/"
      - "C:/Program Files/TortoiseSVN/bin/"
      - "C:/Python/Python310/"
      - "C:/Python/Python310/Scripts/"
      - "C:/Program Files/dotnet/"
      - "C:/gnu/bin/"
      - "C:/Program Files/TortoiseGit/bin/"
      - "E:/nodejs/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "E:/ffmpeg/bin/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2022.3.0/"
      - "E:/miniconda3/Scripts/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/python36/Scripts/"
      - "C:/python36/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/"
      - "C:/Python/Python310/_/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Python/Python310/Scripts/___/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/Programs/MiKTeX/miktex/bin/x64/"
      - "E:/Lingma/bin/"
      - "E:/Microsoft VS Code/bin/"
      - "C:/Program Files/lib/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/lib/"
      - "C:/Program Files (x86)/"
    found: "C:/msys64/mingw64/lib/libm.a"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "E:/github/sextractor_win/win/install/bin"
        - "C:/msys64/mingw64/bin"
        - "/bin"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:35 (find_library)"
    mode: "library"
    variable: "FFTW_LIBRARY"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "fftw3f"
      - "libfftw3f"
      - "fftw3"
      - "libfftw3"
    candidate_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/cmake/bin/"
      - "E:/miniconda3/condabin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/libnvvp/"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/mayong/jdk1.8.0_101/bin/"
      - "C:/gnu/bin___/"
      - "C:/Program Files/Git/cmd/"
      - "C:/msys64/mingw64/bin__/"
      - "E:/github/sextractor_win/win/build/%JAA_HOME%/bin/"
      - "C:/PuTTY/"
      - "C:/Program Files/TortoiseSVN/bin/"
      - "C:/Python/Python310/"
      - "C:/Python/Python310/Scripts/"
      - "C:/Program Files/dotnet/"
      - "C:/gnu/bin/"
      - "C:/Program Files/TortoiseGit/bin/"
      - "E:/nodejs/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "E:/ffmpeg/bin/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2022.3.0/"
      - "E:/miniconda3/Scripts/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/python36/Scripts/"
      - "C:/python36/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/"
      - "C:/Python/Python310/_/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Python/Python310/Scripts/___/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/Programs/MiKTeX/miktex/bin/x64/"
      - "E:/Lingma/bin/"
      - "E:/Microsoft VS Code/bin/"
      - "C:/Program Files/lib/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/lib/"
      - "C:/Program Files (x86)/"
      - "C:/msys64/mingw64/lib/"
      - "C:/msys64/mingw64/"
      - "E:/github/sextractor_win/win/install/lib/"
      - "E:/github/sextractor_win/win/install/"
      - "E:/github/sextractor_win/win/install/bin/"
      - "/bin/"
      - "C:/msys64/mingw64/lib/"
      - "/mingw64/lib/"
    searched_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/cmake/bin/"
      - "E:/miniconda3/condabin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/libnvvp/"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/mayong/jdk1.8.0_101/bin/"
      - "C:/gnu/bin___/"
      - "C:/Program Files/Git/cmd/"
      - "C:/msys64/mingw64/bin__/"
      - "E:/github/sextractor_win/win/build/%JAA_HOME%/bin/"
      - "C:/PuTTY/"
      - "C:/Program Files/TortoiseSVN/bin/"
      - "C:/Python/Python310/"
      - "C:/Python/Python310/Scripts/"
      - "C:/Program Files/dotnet/"
      - "C:/gnu/bin/"
      - "C:/Program Files/TortoiseGit/bin/"
      - "E:/nodejs/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "E:/ffmpeg/bin/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2022.3.0/"
      - "E:/miniconda3/Scripts/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/python36/Scripts/"
      - "C:/python36/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/"
      - "C:/Python/Python310/_/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Python/Python310/Scripts/___/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/Programs/MiKTeX/miktex/bin/x64/"
      - "E:/Lingma/bin/"
      - "E:/Microsoft VS Code/bin/"
      - "C:/Program Files/lib/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/lib/"
      - "C:/Program Files (x86)/"
    found: "C:/msys64/mingw64/lib/libfftw3f.dll.a"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "E:/github/sextractor_win/win/install/bin"
        - "C:/msys64/mingw64/bin"
        - "/bin"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:40 (find_path)"
    mode: "path"
    variable: "FFTW_INCLUDE_DIR"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "fftw3.h"
    candidate_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/cmake/bin/"
      - "E:/miniconda3/condabin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/libnvvp/"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/mayong/jdk1.8.0_101/bin/"
      - "C:/gnu/bin___/"
      - "C:/Program Files/Git/cmd/"
      - "C:/msys64/mingw64/bin__/"
      - "E:/github/sextractor_win/win/build/%JAA_HOME%/bin/"
      - "C:/PuTTY/"
      - "C:/Program Files/TortoiseSVN/bin/"
      - "C:/Python/Python310/"
      - "C:/Python/Python310/Scripts/"
      - "C:/Program Files/dotnet/"
      - "C:/gnu/bin/"
      - "C:/Program Files/TortoiseGit/bin/"
      - "E:/nodejs/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "E:/ffmpeg/bin/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2022.3.0/"
      - "E:/miniconda3/Scripts/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/python36/Scripts/"
      - "C:/python36/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/"
      - "C:/Python/Python310/_/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Python/Python310/Scripts/___/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/Programs/MiKTeX/miktex/bin/x64/"
      - "E:/Lingma/bin/"
      - "E:/Microsoft VS Code/bin/"
      - "C:/Program Files/include/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/include/"
      - "C:/Program Files (x86)/"
      - "C:/msys64/mingw64/include/"
      - "C:/msys64/mingw64/"
      - "E:/github/sextractor_win/win/install/include/"
      - "E:/github/sextractor_win/win/install/"
      - "C:/msys64/mingw64/include/"
      - "/mingw64/include/"
    searched_directories:
      - "C:/msys64/mingw64/bin/fftw3.h"
      - "C:/cmake/bin/fftw3.h"
      - "E:/miniconda3/condabin/fftw3.h"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/fftw3.h"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/libnvvp/fftw3.h"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/fftw3.h"
      - "C:/Windows/System32/fftw3.h"
      - "C:/Windows/fftw3.h"
      - "C:/Windows/System32/wbem/fftw3.h"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/fftw3.h"
      - "C:/Windows/System32/OpenSSH/fftw3.h"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/fftw3.h"
      - "D:/mayong/jdk1.8.0_101/bin/fftw3.h"
      - "C:/gnu/bin___/fftw3.h"
      - "C:/Program Files/Git/cmd/fftw3.h"
      - "C:/msys64/mingw64/bin__/fftw3.h"
      - "E:/github/sextractor_win/win/build/%JAA_HOME%/bin/fftw3.h"
      - "C:/PuTTY/fftw3.h"
      - "C:/Program Files/TortoiseSVN/bin/fftw3.h"
      - "C:/Python/Python310/fftw3.h"
      - "C:/Python/Python310/Scripts/fftw3.h"
      - "C:/Program Files/dotnet/fftw3.h"
      - "C:/gnu/bin/fftw3.h"
      - "C:/Program Files/TortoiseGit/bin/fftw3.h"
      - "E:/nodejs/fftw3.h"
      - "C:/Program Files/Docker/Docker/resources/bin/fftw3.h"
      - "E:/ffmpeg/bin/fftw3.h"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2022.3.0/fftw3.h"
      - "E:/miniconda3/Scripts/fftw3.h"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/fftw3.h"
      - "C:/python36/Scripts/fftw3.h"
      - "C:/python36/fftw3.h"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/Scripts/fftw3.h"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/fftw3.h"
      - "C:/Python/Python310/_/fftw3.h"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/fftw3.h"
      - "C:/Python/Python310/Scripts/___/fftw3.h"
      - "C:/Users/<USER>/.dotnet/tools/fftw3.h"
      - "C:/Users/<USER>/AppData/Roaming/npm/fftw3.h"
      - "E:/Programs/MiKTeX/miktex/bin/x64/fftw3.h"
      - "E:/Lingma/bin/fftw3.h"
      - "E:/Microsoft VS Code/bin/fftw3.h"
      - "C:/Program Files/include/fftw3.h"
      - "C:/Program Files/fftw3.h"
      - "C:/Program Files (x86)/include/fftw3.h"
      - "C:/Program Files (x86)/fftw3.h"
    found: "C:/msys64/mingw64/include/"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:49 (find_library)"
    mode: "library"
    variable: "CFITSIO_LIBRARY"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "cfitsio"
      - "libcfitsio"
    candidate_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/cmake/bin/"
      - "E:/miniconda3/condabin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/libnvvp/"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/mayong/jdk1.8.0_101/bin/"
      - "C:/gnu/bin___/"
      - "C:/Program Files/Git/cmd/"
      - "C:/msys64/mingw64/bin__/"
      - "E:/github/sextractor_win/win/build/%JAA_HOME%/bin/"
      - "C:/PuTTY/"
      - "C:/Program Files/TortoiseSVN/bin/"
      - "C:/Python/Python310/"
      - "C:/Python/Python310/Scripts/"
      - "C:/Program Files/dotnet/"
      - "C:/gnu/bin/"
      - "C:/Program Files/TortoiseGit/bin/"
      - "E:/nodejs/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "E:/ffmpeg/bin/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2022.3.0/"
      - "E:/miniconda3/Scripts/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/python36/Scripts/"
      - "C:/python36/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/"
      - "C:/Python/Python310/_/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Python/Python310/Scripts/___/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/Programs/MiKTeX/miktex/bin/x64/"
      - "E:/Lingma/bin/"
      - "E:/Microsoft VS Code/bin/"
      - "C:/Program Files/lib/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/lib/"
      - "C:/Program Files (x86)/"
      - "C:/msys64/mingw64/lib/"
      - "C:/msys64/mingw64/"
      - "E:/github/sextractor_win/win/install/lib/"
      - "E:/github/sextractor_win/win/install/"
      - "E:/github/sextractor_win/win/install/bin/"
      - "/bin/"
      - "C:/msys64/mingw64/lib/"
      - "/mingw64/lib/"
    searched_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/cmake/bin/"
      - "E:/miniconda3/condabin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/libnvvp/"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/mayong/jdk1.8.0_101/bin/"
      - "C:/gnu/bin___/"
      - "C:/Program Files/Git/cmd/"
      - "C:/msys64/mingw64/bin__/"
      - "E:/github/sextractor_win/win/build/%JAA_HOME%/bin/"
      - "C:/PuTTY/"
      - "C:/Program Files/TortoiseSVN/bin/"
      - "C:/Python/Python310/"
      - "C:/Python/Python310/Scripts/"
      - "C:/Program Files/dotnet/"
      - "C:/gnu/bin/"
      - "C:/Program Files/TortoiseGit/bin/"
      - "E:/nodejs/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "E:/ffmpeg/bin/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2022.3.0/"
      - "E:/miniconda3/Scripts/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/python36/Scripts/"
      - "C:/python36/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/"
      - "C:/Python/Python310/_/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Python/Python310/Scripts/___/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/Programs/MiKTeX/miktex/bin/x64/"
      - "E:/Lingma/bin/"
      - "E:/Microsoft VS Code/bin/"
      - "C:/Program Files/lib/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/lib/"
      - "C:/Program Files (x86)/"
    found: "C:/msys64/mingw64/lib/libcfitsio.dll.a"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "E:/github/sextractor_win/win/install/bin"
        - "C:/msys64/mingw64/bin"
        - "/bin"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:54 (find_path)"
    mode: "path"
    variable: "CFITSIO_INCLUDE_DIR"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "fitsio.h"
    candidate_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/cmake/bin/"
      - "E:/miniconda3/condabin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/libnvvp/"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/mayong/jdk1.8.0_101/bin/"
      - "C:/gnu/bin___/"
      - "C:/Program Files/Git/cmd/"
      - "C:/msys64/mingw64/bin__/"
      - "E:/github/sextractor_win/win/build/%JAA_HOME%/bin/"
      - "C:/PuTTY/"
      - "C:/Program Files/TortoiseSVN/bin/"
      - "C:/Python/Python310/"
      - "C:/Python/Python310/Scripts/"
      - "C:/Program Files/dotnet/"
      - "C:/gnu/bin/"
      - "C:/Program Files/TortoiseGit/bin/"
      - "E:/nodejs/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "E:/ffmpeg/bin/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2022.3.0/"
      - "E:/miniconda3/Scripts/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/python36/Scripts/"
      - "C:/python36/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/"
      - "C:/Python/Python310/_/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Python/Python310/Scripts/___/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/Programs/MiKTeX/miktex/bin/x64/"
      - "E:/Lingma/bin/"
      - "E:/Microsoft VS Code/bin/"
      - "C:/Program Files/include/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/include/"
      - "C:/Program Files (x86)/"
      - "C:/msys64/mingw64/include/"
      - "C:/msys64/mingw64/"
      - "E:/github/sextractor_win/win/install/include/"
      - "E:/github/sextractor_win/win/install/"
      - "C:/msys64/mingw64/include/"
      - "/mingw64/include/"
    searched_directories:
      - "C:/msys64/mingw64/bin/fitsio.h"
      - "C:/cmake/bin/fitsio.h"
      - "E:/miniconda3/condabin/fitsio.h"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/fitsio.h"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/libnvvp/fitsio.h"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/fitsio.h"
      - "C:/Windows/System32/fitsio.h"
      - "C:/Windows/fitsio.h"
      - "C:/Windows/System32/wbem/fitsio.h"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/fitsio.h"
      - "C:/Windows/System32/OpenSSH/fitsio.h"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/fitsio.h"
      - "D:/mayong/jdk1.8.0_101/bin/fitsio.h"
      - "C:/gnu/bin___/fitsio.h"
      - "C:/Program Files/Git/cmd/fitsio.h"
      - "C:/msys64/mingw64/bin__/fitsio.h"
      - "E:/github/sextractor_win/win/build/%JAA_HOME%/bin/fitsio.h"
      - "C:/PuTTY/fitsio.h"
      - "C:/Program Files/TortoiseSVN/bin/fitsio.h"
      - "C:/Python/Python310/fitsio.h"
      - "C:/Python/Python310/Scripts/fitsio.h"
      - "C:/Program Files/dotnet/fitsio.h"
      - "C:/gnu/bin/fitsio.h"
      - "C:/Program Files/TortoiseGit/bin/fitsio.h"
      - "E:/nodejs/fitsio.h"
      - "C:/Program Files/Docker/Docker/resources/bin/fitsio.h"
      - "E:/ffmpeg/bin/fitsio.h"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2022.3.0/fitsio.h"
      - "E:/miniconda3/Scripts/fitsio.h"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/fitsio.h"
      - "C:/python36/Scripts/fitsio.h"
      - "C:/python36/fitsio.h"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/Scripts/fitsio.h"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/fitsio.h"
      - "C:/Python/Python310/_/fitsio.h"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/fitsio.h"
      - "C:/Python/Python310/Scripts/___/fitsio.h"
      - "C:/Users/<USER>/.dotnet/tools/fitsio.h"
      - "C:/Users/<USER>/AppData/Roaming/npm/fitsio.h"
      - "E:/Programs/MiKTeX/miktex/bin/x64/fitsio.h"
      - "E:/Lingma/bin/fitsio.h"
      - "E:/Microsoft VS Code/bin/fitsio.h"
      - "C:/Program Files/include/fitsio.h"
      - "C:/Program Files/fitsio.h"
      - "C:/Program Files (x86)/include/fitsio.h"
      - "C:/Program Files (x86)/fitsio.h"
    found: "C:/msys64/mingw64/include/"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:63 (find_library)"
    mode: "library"
    variable: "LAPACK_LIBRARY"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "lapack"
      - "liblapack"
    candidate_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/cmake/bin/"
      - "E:/miniconda3/condabin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/libnvvp/"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/mayong/jdk1.8.0_101/bin/"
      - "C:/gnu/bin___/"
      - "C:/Program Files/Git/cmd/"
      - "C:/msys64/mingw64/bin__/"
      - "E:/github/sextractor_win/win/build/%JAA_HOME%/bin/"
      - "C:/PuTTY/"
      - "C:/Program Files/TortoiseSVN/bin/"
      - "C:/Python/Python310/"
      - "C:/Python/Python310/Scripts/"
      - "C:/Program Files/dotnet/"
      - "C:/gnu/bin/"
      - "C:/Program Files/TortoiseGit/bin/"
      - "E:/nodejs/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "E:/ffmpeg/bin/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2022.3.0/"
      - "E:/miniconda3/Scripts/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/python36/Scripts/"
      - "C:/python36/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/"
      - "C:/Python/Python310/_/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Python/Python310/Scripts/___/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/Programs/MiKTeX/miktex/bin/x64/"
      - "E:/Lingma/bin/"
      - "E:/Microsoft VS Code/bin/"
      - "C:/Program Files/lib/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/lib/"
      - "C:/Program Files (x86)/"
      - "C:/msys64/mingw64/lib/"
      - "C:/msys64/mingw64/"
      - "E:/github/sextractor_win/win/install/lib/"
      - "E:/github/sextractor_win/win/install/"
      - "E:/github/sextractor_win/win/install/bin/"
      - "/bin/"
      - "C:/msys64/mingw64/lib/"
      - "/mingw64/lib/"
    searched_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/cmake/bin/"
      - "E:/miniconda3/condabin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/libnvvp/"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/mayong/jdk1.8.0_101/bin/"
      - "C:/gnu/bin___/"
      - "C:/Program Files/Git/cmd/"
      - "C:/msys64/mingw64/bin__/"
      - "E:/github/sextractor_win/win/build/%JAA_HOME%/bin/"
      - "C:/PuTTY/"
      - "C:/Program Files/TortoiseSVN/bin/"
      - "C:/Python/Python310/"
      - "C:/Python/Python310/Scripts/"
      - "C:/Program Files/dotnet/"
      - "C:/gnu/bin/"
      - "C:/Program Files/TortoiseGit/bin/"
      - "E:/nodejs/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "E:/ffmpeg/bin/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2022.3.0/"
      - "E:/miniconda3/Scripts/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/python36/Scripts/"
      - "C:/python36/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/"
      - "C:/Python/Python310/_/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Python/Python310/Scripts/___/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/Programs/MiKTeX/miktex/bin/x64/"
      - "E:/Lingma/bin/"
      - "E:/Microsoft VS Code/bin/"
      - "C:/Program Files/lib/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/lib/"
      - "C:/Program Files (x86)/"
    found: "C:/msys64/mingw64/lib/liblapack.dll.a"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "E:/github/sextractor_win/win/install/bin"
        - "C:/msys64/mingw64/bin"
        - "/bin"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:67 (find_library)"
    mode: "library"
    variable: "BLAS_LIBRARY"
    description: "Path to a library."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "openblas"
      - "libopenblas"
      - "blas"
      - "libblas"
    candidate_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/cmake/bin/"
      - "E:/miniconda3/condabin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/libnvvp/"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/mayong/jdk1.8.0_101/bin/"
      - "C:/gnu/bin___/"
      - "C:/Program Files/Git/cmd/"
      - "C:/msys64/mingw64/bin__/"
      - "E:/github/sextractor_win/win/build/%JAA_HOME%/bin/"
      - "C:/PuTTY/"
      - "C:/Program Files/TortoiseSVN/bin/"
      - "C:/Python/Python310/"
      - "C:/Python/Python310/Scripts/"
      - "C:/Program Files/dotnet/"
      - "C:/gnu/bin/"
      - "C:/Program Files/TortoiseGit/bin/"
      - "E:/nodejs/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "E:/ffmpeg/bin/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2022.3.0/"
      - "E:/miniconda3/Scripts/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/python36/Scripts/"
      - "C:/python36/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/"
      - "C:/Python/Python310/_/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Python/Python310/Scripts/___/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/Programs/MiKTeX/miktex/bin/x64/"
      - "E:/Lingma/bin/"
      - "E:/Microsoft VS Code/bin/"
      - "C:/Program Files/lib/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/lib/"
      - "C:/Program Files (x86)/"
      - "C:/msys64/mingw64/lib/"
      - "C:/msys64/mingw64/"
      - "E:/github/sextractor_win/win/install/lib/"
      - "E:/github/sextractor_win/win/install/"
      - "E:/github/sextractor_win/win/install/bin/"
      - "/bin/"
      - "C:/msys64/mingw64/lib/"
      - "/mingw64/lib/"
    searched_directories:
      - "C:/msys64/mingw64/bin/"
      - "C:/cmake/bin/"
      - "E:/miniconda3/condabin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/bin/"
      - "C:/Program Files/NVIDIA GPU Computing Toolkit/CUDA/v11.8/libnvvp/"
      - "C:/Program Files (x86)/VMware/VMware Workstation/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Program Files (x86)/NVIDIA Corporation/PhysX/Common/"
      - "D:/mayong/jdk1.8.0_101/bin/"
      - "C:/gnu/bin___/"
      - "C:/Program Files/Git/cmd/"
      - "C:/msys64/mingw64/bin__/"
      - "E:/github/sextractor_win/win/build/%JAA_HOME%/bin/"
      - "C:/PuTTY/"
      - "C:/Program Files/TortoiseSVN/bin/"
      - "C:/Python/Python310/"
      - "C:/Python/Python310/Scripts/"
      - "C:/Program Files/dotnet/"
      - "C:/gnu/bin/"
      - "C:/Program Files/TortoiseGit/bin/"
      - "E:/nodejs/"
      - "C:/Program Files/Docker/Docker/resources/bin/"
      - "E:/ffmpeg/bin/"
      - "C:/Program Files/NVIDIA Corporation/Nsight Compute 2022.3.0/"
      - "E:/miniconda3/Scripts/"
      - "C:/Program Files/NVIDIA Corporation/NVIDIA NvDLISR/"
      - "C:/python36/Scripts/"
      - "C:/python36/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/Scripts/"
      - "C:/Users/<USER>/AppData/Local/Programs/Python/Python39/"
      - "C:/Python/Python310/_/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Python/Python310/Scripts/___/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "C:/Users/<USER>/AppData/Roaming/npm/"
      - "E:/Programs/MiKTeX/miktex/bin/x64/"
      - "E:/Lingma/bin/"
      - "E:/Microsoft VS Code/bin/"
      - "C:/Program Files/lib/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/lib/"
      - "C:/Program Files (x86)/"
    found: "C:/msys64/mingw64/lib/libopenblas.dll.a"
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "E:/github/sextractor_win/win/install/bin"
        - "C:/msys64/mingw64/bin"
        - "/bin"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:195 (find_file)"
    mode: "file"
    variable: "CFITSIO_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libcfitsio-10.dll"
      - "cfitsio.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/libcfitsio-10.dll"
      - "/mingw64/bin/cfitsio.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:201 (find_file)"
    mode: "file"
    variable: "FFTW_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libfftw3f-3.dll"
      - "fftw3f.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/libfftw3f-3.dll"
      - "/mingw64/bin/fftw3f.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:207 (find_file)"
    mode: "file"
    variable: "OPENBLAS_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libopenblas.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/libopenblas.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:213 (find_file)"
    mode: "file"
    variable: "LAPACK_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "liblapack.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/liblapack.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:219 (find_file)"
    mode: "file"
    variable: "GFORTRAN_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libgfortran-5.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/libgfortran-5.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:225 (find_file)"
    mode: "file"
    variable: "QUADMATH_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libquadmath-0.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/libquadmath-0.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:231 (find_file)"
    mode: "file"
    variable: "GCC_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libgcc_s_seh-1.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/libgcc_s_seh-1.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:237 (find_file)"
    mode: "file"
    variable: "ZLIB_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "zlib1.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/zlib1.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:243 (find_file)"
    mode: "file"
    variable: "CURL_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libcurl-4.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/libcurl-4.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:249 (find_file)"
    mode: "file"
    variable: "WINPTHREAD_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libwinpthread-1.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/libwinpthread-1.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:256 (find_file)"
    mode: "file"
    variable: "BROTLIDEC_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libbrotlidec.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/libbrotlidec.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:262 (find_file)"
    mode: "file"
    variable: "BROTLICOMMON_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libbrotlicommon.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/libbrotlicommon.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:268 (find_file)"
    mode: "file"
    variable: "IDN2_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libidn2-0.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/libidn2-0.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:274 (find_file)"
    mode: "file"
    variable: "NGHTTP2_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libnghttp2-14.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/libnghttp2-14.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:280 (find_file)"
    mode: "file"
    variable: "NGHTTP3_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libnghttp3-9.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/libnghttp3-9.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:286 (find_file)"
    mode: "file"
    variable: "NGTCP2_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libngtcp2-16.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/libngtcp2-16.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:292 (find_file)"
    mode: "file"
    variable: "NGTCP2_CRYPTO_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libngtcp2_crypto_ossl-0.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/libngtcp2_crypto_ossl-0.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:298 (find_file)"
    mode: "file"
    variable: "PSL_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libpsl-5.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/libpsl-5.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:304 (find_file)"
    mode: "file"
    variable: "SSH2_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libssh2-1.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/libssh2-1.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:310 (find_file)"
    mode: "file"
    variable: "ICONV_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libiconv-2.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/libiconv-2.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:316 (find_file)"
    mode: "file"
    variable: "INTL_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libintl-8.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/libintl-8.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:322 (find_file)"
    mode: "file"
    variable: "UNISTRING_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libunistring-5.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/libunistring-5.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:328 (find_file)"
    mode: "file"
    variable: "ZSTD_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libzstd.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/libzstd.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:334 (find_file)"
    mode: "file"
    variable: "CRYPTO_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libcrypto-3-x64.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/libcrypto-3-x64.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
  -
    kind: "find-v1"
    backtrace:
      - "CMakeLists.txt:340 (find_file)"
    mode: "file"
    variable: "SSL_DLL"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libssl-3-x64.dll"
    candidate_directories:
      - "/mingw64/bin/"
    searched_directories:
      - "/mingw64/bin/libssl-3-x64.dll"
    found: false
    search_context:
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\CMake\\bin"
        - "E:\\miniconda3\\condabin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\bin"
        - "C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v11.8\\libnvvp"
        - "C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\"
        - "C:\\Windows\\system32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\Windows\\System32\\OpenSSH\\"
        - "C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "D:\\mayong\\jdk1.8.0_101\\bin"
        - "C:\\gnu\\bin___"
        - "C:\\Program Files\\Git\\cmd"
        - "C:\\msys64\\mingw64\\bin__"
        - "%JAA_HOME%\\bin"
        - "C:\\PuTTY\\"
        - "C:\\Program Files\\TortoiseSVN\\bin"
        - "C:\\Python\\Python310\\"
        - "C:\\Python\\Python310\\Scripts"
        - "C:\\Program Files\\dotnet\\"
        - "C:\\gnu\\bin"
        - "C:\\Program Files\\TortoiseGit\\bin"
        - "E:\\nodejs\\"
        - "C:\\Program Files\\Docker\\Docker\\resources\\bin"
        - "E:\\ffmpeg\\bin"
        - "C:\\WINDOWS\\system32"
        - "C:\\WINDOWS"
        - "C:\\WINDOWS\\System32\\Wbem"
        - "C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\WINDOWS\\System32\\OpenSSH\\"
        - "C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2022.3.0\\"
        - "E:\\miniconda3\\Scripts"
        - "C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR"
        - "C:\\python36\\Scripts\\"
        - "C:\\python36\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\"
        - "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\"
        - "C:\\Python\\Python310\\_"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:\\Python\\Python310\\Scripts\\___"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "C:\\Users\\<USER>\\AppData\\Roaming\\npm"
        - "E:\\Programs\\MiKTeX\\miktex\\bin\\x64\\"
        - "E:\\Lingma\\bin"
        - "E:\\Microsoft VS Code\\bin"
      CMAKE_INSTALL_PREFIX: "E:/github/sextractor_win/win/install"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "E:/github/sextractor_win/win/install"
...
