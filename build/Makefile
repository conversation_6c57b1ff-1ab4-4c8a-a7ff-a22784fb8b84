# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\cmake\bin\cmake.exe

# The command to remove a file.
RM = C:\cmake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\github\sextractor_win\win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\github\sextractor_win\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	C:\cmake\bin\cmake-gui.exe -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	C:\cmake\bin\cmake.exe --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	C:\cmake\bin\cmake.exe -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	C:\cmake\bin\cmake.exe -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	C:\cmake\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	C:\cmake\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	C:\cmake\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	C:\cmake\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start E:\github\sextractor_win\build\CMakeFiles E:\github\sextractor_win\build\\CMakeFiles\progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start E:\github\sextractor_win\build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named fits

# Build rule for target.
fits: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 fits
.PHONY : fits

# fast build rule for target.
fits/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/build
.PHONY : fits/fast

#=============================================================================
# Target rules for targets named wcs

# Build rule for target.
wcs: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 wcs
.PHONY : wcs

# fast build rule for target.
wcs/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/build
.PHONY : wcs/fast

#=============================================================================
# Target rules for targets named levmar

# Build rule for target.
levmar: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 levmar
.PHONY : levmar

# fast build rule for target.
levmar/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/build
.PHONY : levmar/fast

#=============================================================================
# Target rules for targets named sex

# Build rule for target.
sex: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 sex
.PHONY : sex

# fast build rule for target.
sex/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/build
.PHONY : sex/fast

E_/github/sextractor_win/src/analyse.obj: E_/github/sextractor_win/src/analyse.c.obj
.PHONY : E_/github/sextractor_win/src/analyse.obj

# target to build an object file
E_/github/sextractor_win/src/analyse.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/analyse.c.obj
.PHONY : E_/github/sextractor_win/src/analyse.c.obj

E_/github/sextractor_win/src/analyse.i: E_/github/sextractor_win/src/analyse.c.i
.PHONY : E_/github/sextractor_win/src/analyse.i

# target to preprocess a source file
E_/github/sextractor_win/src/analyse.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/analyse.c.i
.PHONY : E_/github/sextractor_win/src/analyse.c.i

E_/github/sextractor_win/src/analyse.s: E_/github/sextractor_win/src/analyse.c.s
.PHONY : E_/github/sextractor_win/src/analyse.s

# target to generate assembly for a file
E_/github/sextractor_win/src/analyse.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/analyse.c.s
.PHONY : E_/github/sextractor_win/src/analyse.c.s

E_/github/sextractor_win/src/assoc.obj: E_/github/sextractor_win/src/assoc.c.obj
.PHONY : E_/github/sextractor_win/src/assoc.obj

# target to build an object file
E_/github/sextractor_win/src/assoc.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/assoc.c.obj
.PHONY : E_/github/sextractor_win/src/assoc.c.obj

E_/github/sextractor_win/src/assoc.i: E_/github/sextractor_win/src/assoc.c.i
.PHONY : E_/github/sextractor_win/src/assoc.i

# target to preprocess a source file
E_/github/sextractor_win/src/assoc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/assoc.c.i
.PHONY : E_/github/sextractor_win/src/assoc.c.i

E_/github/sextractor_win/src/assoc.s: E_/github/sextractor_win/src/assoc.c.s
.PHONY : E_/github/sextractor_win/src/assoc.s

# target to generate assembly for a file
E_/github/sextractor_win/src/assoc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/assoc.c.s
.PHONY : E_/github/sextractor_win/src/assoc.c.s

E_/github/sextractor_win/src/astrom.obj: E_/github/sextractor_win/src/astrom.c.obj
.PHONY : E_/github/sextractor_win/src/astrom.obj

# target to build an object file
E_/github/sextractor_win/src/astrom.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/astrom.c.obj
.PHONY : E_/github/sextractor_win/src/astrom.c.obj

E_/github/sextractor_win/src/astrom.i: E_/github/sextractor_win/src/astrom.c.i
.PHONY : E_/github/sextractor_win/src/astrom.i

# target to preprocess a source file
E_/github/sextractor_win/src/astrom.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/astrom.c.i
.PHONY : E_/github/sextractor_win/src/astrom.c.i

E_/github/sextractor_win/src/astrom.s: E_/github/sextractor_win/src/astrom.c.s
.PHONY : E_/github/sextractor_win/src/astrom.s

# target to generate assembly for a file
E_/github/sextractor_win/src/astrom.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/astrom.c.s
.PHONY : E_/github/sextractor_win/src/astrom.c.s

E_/github/sextractor_win/src/back.obj: E_/github/sextractor_win/src/back.c.obj
.PHONY : E_/github/sextractor_win/src/back.obj

# target to build an object file
E_/github/sextractor_win/src/back.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/back.c.obj
.PHONY : E_/github/sextractor_win/src/back.c.obj

E_/github/sextractor_win/src/back.i: E_/github/sextractor_win/src/back.c.i
.PHONY : E_/github/sextractor_win/src/back.i

# target to preprocess a source file
E_/github/sextractor_win/src/back.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/back.c.i
.PHONY : E_/github/sextractor_win/src/back.c.i

E_/github/sextractor_win/src/back.s: E_/github/sextractor_win/src/back.c.s
.PHONY : E_/github/sextractor_win/src/back.s

# target to generate assembly for a file
E_/github/sextractor_win/src/back.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/back.c.s
.PHONY : E_/github/sextractor_win/src/back.c.s

E_/github/sextractor_win/src/bpro.obj: E_/github/sextractor_win/src/bpro.c.obj
.PHONY : E_/github/sextractor_win/src/bpro.obj

# target to build an object file
E_/github/sextractor_win/src/bpro.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/bpro.c.obj
.PHONY : E_/github/sextractor_win/src/bpro.c.obj

E_/github/sextractor_win/src/bpro.i: E_/github/sextractor_win/src/bpro.c.i
.PHONY : E_/github/sextractor_win/src/bpro.i

# target to preprocess a source file
E_/github/sextractor_win/src/bpro.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/bpro.c.i
.PHONY : E_/github/sextractor_win/src/bpro.c.i

E_/github/sextractor_win/src/bpro.s: E_/github/sextractor_win/src/bpro.c.s
.PHONY : E_/github/sextractor_win/src/bpro.s

# target to generate assembly for a file
E_/github/sextractor_win/src/bpro.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/bpro.c.s
.PHONY : E_/github/sextractor_win/src/bpro.c.s

E_/github/sextractor_win/src/catout.obj: E_/github/sextractor_win/src/catout.c.obj
.PHONY : E_/github/sextractor_win/src/catout.obj

# target to build an object file
E_/github/sextractor_win/src/catout.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/catout.c.obj
.PHONY : E_/github/sextractor_win/src/catout.c.obj

E_/github/sextractor_win/src/catout.i: E_/github/sextractor_win/src/catout.c.i
.PHONY : E_/github/sextractor_win/src/catout.i

# target to preprocess a source file
E_/github/sextractor_win/src/catout.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/catout.c.i
.PHONY : E_/github/sextractor_win/src/catout.c.i

E_/github/sextractor_win/src/catout.s: E_/github/sextractor_win/src/catout.c.s
.PHONY : E_/github/sextractor_win/src/catout.s

# target to generate assembly for a file
E_/github/sextractor_win/src/catout.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/catout.c.s
.PHONY : E_/github/sextractor_win/src/catout.c.s

E_/github/sextractor_win/src/check.obj: E_/github/sextractor_win/src/check.c.obj
.PHONY : E_/github/sextractor_win/src/check.obj

# target to build an object file
E_/github/sextractor_win/src/check.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/check.c.obj
.PHONY : E_/github/sextractor_win/src/check.c.obj

E_/github/sextractor_win/src/check.i: E_/github/sextractor_win/src/check.c.i
.PHONY : E_/github/sextractor_win/src/check.i

# target to preprocess a source file
E_/github/sextractor_win/src/check.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/check.c.i
.PHONY : E_/github/sextractor_win/src/check.c.i

E_/github/sextractor_win/src/check.s: E_/github/sextractor_win/src/check.c.s
.PHONY : E_/github/sextractor_win/src/check.s

# target to generate assembly for a file
E_/github/sextractor_win/src/check.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/check.c.s
.PHONY : E_/github/sextractor_win/src/check.c.s

E_/github/sextractor_win/src/clean.obj: E_/github/sextractor_win/src/clean.c.obj
.PHONY : E_/github/sextractor_win/src/clean.obj

# target to build an object file
E_/github/sextractor_win/src/clean.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/clean.c.obj
.PHONY : E_/github/sextractor_win/src/clean.c.obj

E_/github/sextractor_win/src/clean.i: E_/github/sextractor_win/src/clean.c.i
.PHONY : E_/github/sextractor_win/src/clean.i

# target to preprocess a source file
E_/github/sextractor_win/src/clean.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/clean.c.i
.PHONY : E_/github/sextractor_win/src/clean.c.i

E_/github/sextractor_win/src/clean.s: E_/github/sextractor_win/src/clean.c.s
.PHONY : E_/github/sextractor_win/src/clean.s

# target to generate assembly for a file
E_/github/sextractor_win/src/clean.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/clean.c.s
.PHONY : E_/github/sextractor_win/src/clean.c.s

E_/github/sextractor_win/src/dgeo.obj: E_/github/sextractor_win/src/dgeo.c.obj
.PHONY : E_/github/sextractor_win/src/dgeo.obj

# target to build an object file
E_/github/sextractor_win/src/dgeo.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/dgeo.c.obj
.PHONY : E_/github/sextractor_win/src/dgeo.c.obj

E_/github/sextractor_win/src/dgeo.i: E_/github/sextractor_win/src/dgeo.c.i
.PHONY : E_/github/sextractor_win/src/dgeo.i

# target to preprocess a source file
E_/github/sextractor_win/src/dgeo.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/dgeo.c.i
.PHONY : E_/github/sextractor_win/src/dgeo.c.i

E_/github/sextractor_win/src/dgeo.s: E_/github/sextractor_win/src/dgeo.c.s
.PHONY : E_/github/sextractor_win/src/dgeo.s

# target to generate assembly for a file
E_/github/sextractor_win/src/dgeo.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/dgeo.c.s
.PHONY : E_/github/sextractor_win/src/dgeo.c.s

E_/github/sextractor_win/src/extract.obj: E_/github/sextractor_win/src/extract.c.obj
.PHONY : E_/github/sextractor_win/src/extract.obj

# target to build an object file
E_/github/sextractor_win/src/extract.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/extract.c.obj
.PHONY : E_/github/sextractor_win/src/extract.c.obj

E_/github/sextractor_win/src/extract.i: E_/github/sextractor_win/src/extract.c.i
.PHONY : E_/github/sextractor_win/src/extract.i

# target to preprocess a source file
E_/github/sextractor_win/src/extract.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/extract.c.i
.PHONY : E_/github/sextractor_win/src/extract.c.i

E_/github/sextractor_win/src/extract.s: E_/github/sextractor_win/src/extract.c.s
.PHONY : E_/github/sextractor_win/src/extract.s

# target to generate assembly for a file
E_/github/sextractor_win/src/extract.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/extract.c.s
.PHONY : E_/github/sextractor_win/src/extract.c.s

E_/github/sextractor_win/src/fft.obj: E_/github/sextractor_win/src/fft.c.obj
.PHONY : E_/github/sextractor_win/src/fft.obj

# target to build an object file
E_/github/sextractor_win/src/fft.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/fft.c.obj
.PHONY : E_/github/sextractor_win/src/fft.c.obj

E_/github/sextractor_win/src/fft.i: E_/github/sextractor_win/src/fft.c.i
.PHONY : E_/github/sextractor_win/src/fft.i

# target to preprocess a source file
E_/github/sextractor_win/src/fft.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/fft.c.i
.PHONY : E_/github/sextractor_win/src/fft.c.i

E_/github/sextractor_win/src/fft.s: E_/github/sextractor_win/src/fft.c.s
.PHONY : E_/github/sextractor_win/src/fft.s

# target to generate assembly for a file
E_/github/sextractor_win/src/fft.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/fft.c.s
.PHONY : E_/github/sextractor_win/src/fft.c.s

E_/github/sextractor_win/src/field.obj: E_/github/sextractor_win/src/field.c.obj
.PHONY : E_/github/sextractor_win/src/field.obj

# target to build an object file
E_/github/sextractor_win/src/field.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/field.c.obj
.PHONY : E_/github/sextractor_win/src/field.c.obj

E_/github/sextractor_win/src/field.i: E_/github/sextractor_win/src/field.c.i
.PHONY : E_/github/sextractor_win/src/field.i

# target to preprocess a source file
E_/github/sextractor_win/src/field.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/field.c.i
.PHONY : E_/github/sextractor_win/src/field.c.i

E_/github/sextractor_win/src/field.s: E_/github/sextractor_win/src/field.c.s
.PHONY : E_/github/sextractor_win/src/field.s

# target to generate assembly for a file
E_/github/sextractor_win/src/field.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/field.c.s
.PHONY : E_/github/sextractor_win/src/field.c.s

E_/github/sextractor_win/src/filter.obj: E_/github/sextractor_win/src/filter.c.obj
.PHONY : E_/github/sextractor_win/src/filter.obj

# target to build an object file
E_/github/sextractor_win/src/filter.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/filter.c.obj
.PHONY : E_/github/sextractor_win/src/filter.c.obj

E_/github/sextractor_win/src/filter.i: E_/github/sextractor_win/src/filter.c.i
.PHONY : E_/github/sextractor_win/src/filter.i

# target to preprocess a source file
E_/github/sextractor_win/src/filter.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/filter.c.i
.PHONY : E_/github/sextractor_win/src/filter.c.i

E_/github/sextractor_win/src/filter.s: E_/github/sextractor_win/src/filter.c.s
.PHONY : E_/github/sextractor_win/src/filter.s

# target to generate assembly for a file
E_/github/sextractor_win/src/filter.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/filter.c.s
.PHONY : E_/github/sextractor_win/src/filter.c.s

E_/github/sextractor_win/src/fits/fitsbody.obj: E_/github/sextractor_win/src/fits/fitsbody.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitsbody.obj

# target to build an object file
E_/github/sextractor_win/src/fits/fitsbody.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsbody.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitsbody.c.obj

E_/github/sextractor_win/src/fits/fitsbody.i: E_/github/sextractor_win/src/fits/fitsbody.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitsbody.i

# target to preprocess a source file
E_/github/sextractor_win/src/fits/fitsbody.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsbody.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitsbody.c.i

E_/github/sextractor_win/src/fits/fitsbody.s: E_/github/sextractor_win/src/fits/fitsbody.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitsbody.s

# target to generate assembly for a file
E_/github/sextractor_win/src/fits/fitsbody.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsbody.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitsbody.c.s

E_/github/sextractor_win/src/fits/fitscat.obj: E_/github/sextractor_win/src/fits/fitscat.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitscat.obj

# target to build an object file
E_/github/sextractor_win/src/fits/fitscat.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscat.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitscat.c.obj

E_/github/sextractor_win/src/fits/fitscat.i: E_/github/sextractor_win/src/fits/fitscat.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitscat.i

# target to preprocess a source file
E_/github/sextractor_win/src/fits/fitscat.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscat.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitscat.c.i

E_/github/sextractor_win/src/fits/fitscat.s: E_/github/sextractor_win/src/fits/fitscat.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitscat.s

# target to generate assembly for a file
E_/github/sextractor_win/src/fits/fitscat.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscat.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitscat.c.s

E_/github/sextractor_win/src/fits/fitscheck.obj: E_/github/sextractor_win/src/fits/fitscheck.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitscheck.obj

# target to build an object file
E_/github/sextractor_win/src/fits/fitscheck.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscheck.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitscheck.c.obj

E_/github/sextractor_win/src/fits/fitscheck.i: E_/github/sextractor_win/src/fits/fitscheck.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitscheck.i

# target to preprocess a source file
E_/github/sextractor_win/src/fits/fitscheck.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscheck.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitscheck.c.i

E_/github/sextractor_win/src/fits/fitscheck.s: E_/github/sextractor_win/src/fits/fitscheck.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitscheck.s

# target to generate assembly for a file
E_/github/sextractor_win/src/fits/fitscheck.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscheck.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitscheck.c.s

E_/github/sextractor_win/src/fits/fitscleanup.obj: E_/github/sextractor_win/src/fits/fitscleanup.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitscleanup.obj

# target to build an object file
E_/github/sextractor_win/src/fits/fitscleanup.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscleanup.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitscleanup.c.obj

E_/github/sextractor_win/src/fits/fitscleanup.i: E_/github/sextractor_win/src/fits/fitscleanup.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitscleanup.i

# target to preprocess a source file
E_/github/sextractor_win/src/fits/fitscleanup.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscleanup.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitscleanup.c.i

E_/github/sextractor_win/src/fits/fitscleanup.s: E_/github/sextractor_win/src/fits/fitscleanup.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitscleanup.s

# target to generate assembly for a file
E_/github/sextractor_win/src/fits/fitscleanup.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscleanup.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitscleanup.c.s

E_/github/sextractor_win/src/fits/fitsconv.obj: E_/github/sextractor_win/src/fits/fitsconv.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitsconv.obj

# target to build an object file
E_/github/sextractor_win/src/fits/fitsconv.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsconv.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitsconv.c.obj

E_/github/sextractor_win/src/fits/fitsconv.i: E_/github/sextractor_win/src/fits/fitsconv.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitsconv.i

# target to preprocess a source file
E_/github/sextractor_win/src/fits/fitsconv.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsconv.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitsconv.c.i

E_/github/sextractor_win/src/fits/fitsconv.s: E_/github/sextractor_win/src/fits/fitsconv.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitsconv.s

# target to generate assembly for a file
E_/github/sextractor_win/src/fits/fitsconv.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsconv.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitsconv.c.s

E_/github/sextractor_win/src/fits/fitshead.obj: E_/github/sextractor_win/src/fits/fitshead.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitshead.obj

# target to build an object file
E_/github/sextractor_win/src/fits/fitshead.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitshead.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitshead.c.obj

E_/github/sextractor_win/src/fits/fitshead.i: E_/github/sextractor_win/src/fits/fitshead.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitshead.i

# target to preprocess a source file
E_/github/sextractor_win/src/fits/fitshead.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitshead.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitshead.c.i

E_/github/sextractor_win/src/fits/fitshead.s: E_/github/sextractor_win/src/fits/fitshead.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitshead.s

# target to generate assembly for a file
E_/github/sextractor_win/src/fits/fitshead.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitshead.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitshead.c.s

E_/github/sextractor_win/src/fits/fitskey.obj: E_/github/sextractor_win/src/fits/fitskey.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitskey.obj

# target to build an object file
E_/github/sextractor_win/src/fits/fitskey.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitskey.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitskey.c.obj

E_/github/sextractor_win/src/fits/fitskey.i: E_/github/sextractor_win/src/fits/fitskey.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitskey.i

# target to preprocess a source file
E_/github/sextractor_win/src/fits/fitskey.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitskey.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitskey.c.i

E_/github/sextractor_win/src/fits/fitskey.s: E_/github/sextractor_win/src/fits/fitskey.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitskey.s

# target to generate assembly for a file
E_/github/sextractor_win/src/fits/fitskey.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitskey.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitskey.c.s

E_/github/sextractor_win/src/fits/fitsmisc.obj: E_/github/sextractor_win/src/fits/fitsmisc.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitsmisc.obj

# target to build an object file
E_/github/sextractor_win/src/fits/fitsmisc.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsmisc.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitsmisc.c.obj

E_/github/sextractor_win/src/fits/fitsmisc.i: E_/github/sextractor_win/src/fits/fitsmisc.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitsmisc.i

# target to preprocess a source file
E_/github/sextractor_win/src/fits/fitsmisc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsmisc.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitsmisc.c.i

E_/github/sextractor_win/src/fits/fitsmisc.s: E_/github/sextractor_win/src/fits/fitsmisc.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitsmisc.s

# target to generate assembly for a file
E_/github/sextractor_win/src/fits/fitsmisc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsmisc.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitsmisc.c.s

E_/github/sextractor_win/src/fits/fitsread.obj: E_/github/sextractor_win/src/fits/fitsread.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitsread.obj

# target to build an object file
E_/github/sextractor_win/src/fits/fitsread.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsread.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitsread.c.obj

E_/github/sextractor_win/src/fits/fitsread.i: E_/github/sextractor_win/src/fits/fitsread.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitsread.i

# target to preprocess a source file
E_/github/sextractor_win/src/fits/fitsread.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsread.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitsread.c.i

E_/github/sextractor_win/src/fits/fitsread.s: E_/github/sextractor_win/src/fits/fitsread.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitsread.s

# target to generate assembly for a file
E_/github/sextractor_win/src/fits/fitsread.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsread.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitsread.c.s

E_/github/sextractor_win/src/fits/fitstab.obj: E_/github/sextractor_win/src/fits/fitstab.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitstab.obj

# target to build an object file
E_/github/sextractor_win/src/fits/fitstab.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitstab.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitstab.c.obj

E_/github/sextractor_win/src/fits/fitstab.i: E_/github/sextractor_win/src/fits/fitstab.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitstab.i

# target to preprocess a source file
E_/github/sextractor_win/src/fits/fitstab.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitstab.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitstab.c.i

E_/github/sextractor_win/src/fits/fitstab.s: E_/github/sextractor_win/src/fits/fitstab.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitstab.s

# target to generate assembly for a file
E_/github/sextractor_win/src/fits/fitstab.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitstab.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitstab.c.s

E_/github/sextractor_win/src/fits/fitsutil.obj: E_/github/sextractor_win/src/fits/fitsutil.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitsutil.obj

# target to build an object file
E_/github/sextractor_win/src/fits/fitsutil.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsutil.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitsutil.c.obj

E_/github/sextractor_win/src/fits/fitsutil.i: E_/github/sextractor_win/src/fits/fitsutil.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitsutil.i

# target to preprocess a source file
E_/github/sextractor_win/src/fits/fitsutil.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsutil.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitsutil.c.i

E_/github/sextractor_win/src/fits/fitsutil.s: E_/github/sextractor_win/src/fits/fitsutil.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitsutil.s

# target to generate assembly for a file
E_/github/sextractor_win/src/fits/fitsutil.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsutil.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitsutil.c.s

E_/github/sextractor_win/src/fits/fitswrite.obj: E_/github/sextractor_win/src/fits/fitswrite.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitswrite.obj

# target to build an object file
E_/github/sextractor_win/src/fits/fitswrite.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitswrite.c.obj
.PHONY : E_/github/sextractor_win/src/fits/fitswrite.c.obj

E_/github/sextractor_win/src/fits/fitswrite.i: E_/github/sextractor_win/src/fits/fitswrite.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitswrite.i

# target to preprocess a source file
E_/github/sextractor_win/src/fits/fitswrite.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitswrite.c.i
.PHONY : E_/github/sextractor_win/src/fits/fitswrite.c.i

E_/github/sextractor_win/src/fits/fitswrite.s: E_/github/sextractor_win/src/fits/fitswrite.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitswrite.s

# target to generate assembly for a file
E_/github/sextractor_win/src/fits/fitswrite.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitswrite.c.s
.PHONY : E_/github/sextractor_win/src/fits/fitswrite.c.s

E_/github/sextractor_win/src/fitswcs.obj: E_/github/sextractor_win/src/fitswcs.c.obj
.PHONY : E_/github/sextractor_win/src/fitswcs.obj

# target to build an object file
E_/github/sextractor_win/src/fitswcs.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/fitswcs.c.obj
.PHONY : E_/github/sextractor_win/src/fitswcs.c.obj

E_/github/sextractor_win/src/fitswcs.i: E_/github/sextractor_win/src/fitswcs.c.i
.PHONY : E_/github/sextractor_win/src/fitswcs.i

# target to preprocess a source file
E_/github/sextractor_win/src/fitswcs.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/fitswcs.c.i
.PHONY : E_/github/sextractor_win/src/fitswcs.c.i

E_/github/sextractor_win/src/fitswcs.s: E_/github/sextractor_win/src/fitswcs.c.s
.PHONY : E_/github/sextractor_win/src/fitswcs.s

# target to generate assembly for a file
E_/github/sextractor_win/src/fitswcs.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/fitswcs.c.s
.PHONY : E_/github/sextractor_win/src/fitswcs.c.s

E_/github/sextractor_win/src/flag.obj: E_/github/sextractor_win/src/flag.c.obj
.PHONY : E_/github/sextractor_win/src/flag.obj

# target to build an object file
E_/github/sextractor_win/src/flag.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/flag.c.obj
.PHONY : E_/github/sextractor_win/src/flag.c.obj

E_/github/sextractor_win/src/flag.i: E_/github/sextractor_win/src/flag.c.i
.PHONY : E_/github/sextractor_win/src/flag.i

# target to preprocess a source file
E_/github/sextractor_win/src/flag.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/flag.c.i
.PHONY : E_/github/sextractor_win/src/flag.c.i

E_/github/sextractor_win/src/flag.s: E_/github/sextractor_win/src/flag.c.s
.PHONY : E_/github/sextractor_win/src/flag.s

# target to generate assembly for a file
E_/github/sextractor_win/src/flag.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/flag.c.s
.PHONY : E_/github/sextractor_win/src/flag.c.s

E_/github/sextractor_win/src/graph.obj: E_/github/sextractor_win/src/graph.c.obj
.PHONY : E_/github/sextractor_win/src/graph.obj

# target to build an object file
E_/github/sextractor_win/src/graph.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/graph.c.obj
.PHONY : E_/github/sextractor_win/src/graph.c.obj

E_/github/sextractor_win/src/graph.i: E_/github/sextractor_win/src/graph.c.i
.PHONY : E_/github/sextractor_win/src/graph.i

# target to preprocess a source file
E_/github/sextractor_win/src/graph.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/graph.c.i
.PHONY : E_/github/sextractor_win/src/graph.c.i

E_/github/sextractor_win/src/graph.s: E_/github/sextractor_win/src/graph.c.s
.PHONY : E_/github/sextractor_win/src/graph.s

# target to generate assembly for a file
E_/github/sextractor_win/src/graph.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/graph.c.s
.PHONY : E_/github/sextractor_win/src/graph.c.s

E_/github/sextractor_win/src/growth.obj: E_/github/sextractor_win/src/growth.c.obj
.PHONY : E_/github/sextractor_win/src/growth.obj

# target to build an object file
E_/github/sextractor_win/src/growth.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/growth.c.obj
.PHONY : E_/github/sextractor_win/src/growth.c.obj

E_/github/sextractor_win/src/growth.i: E_/github/sextractor_win/src/growth.c.i
.PHONY : E_/github/sextractor_win/src/growth.i

# target to preprocess a source file
E_/github/sextractor_win/src/growth.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/growth.c.i
.PHONY : E_/github/sextractor_win/src/growth.c.i

E_/github/sextractor_win/src/growth.s: E_/github/sextractor_win/src/growth.c.s
.PHONY : E_/github/sextractor_win/src/growth.s

# target to generate assembly for a file
E_/github/sextractor_win/src/growth.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/growth.c.s
.PHONY : E_/github/sextractor_win/src/growth.c.s

E_/github/sextractor_win/src/header.obj: E_/github/sextractor_win/src/header.c.obj
.PHONY : E_/github/sextractor_win/src/header.obj

# target to build an object file
E_/github/sextractor_win/src/header.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/header.c.obj
.PHONY : E_/github/sextractor_win/src/header.c.obj

E_/github/sextractor_win/src/header.i: E_/github/sextractor_win/src/header.c.i
.PHONY : E_/github/sextractor_win/src/header.i

# target to preprocess a source file
E_/github/sextractor_win/src/header.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/header.c.i
.PHONY : E_/github/sextractor_win/src/header.c.i

E_/github/sextractor_win/src/header.s: E_/github/sextractor_win/src/header.c.s
.PHONY : E_/github/sextractor_win/src/header.s

# target to generate assembly for a file
E_/github/sextractor_win/src/header.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/header.c.s
.PHONY : E_/github/sextractor_win/src/header.c.s

E_/github/sextractor_win/src/image.obj: E_/github/sextractor_win/src/image.c.obj
.PHONY : E_/github/sextractor_win/src/image.obj

# target to build an object file
E_/github/sextractor_win/src/image.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/image.c.obj
.PHONY : E_/github/sextractor_win/src/image.c.obj

E_/github/sextractor_win/src/image.i: E_/github/sextractor_win/src/image.c.i
.PHONY : E_/github/sextractor_win/src/image.i

# target to preprocess a source file
E_/github/sextractor_win/src/image.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/image.c.i
.PHONY : E_/github/sextractor_win/src/image.c.i

E_/github/sextractor_win/src/image.s: E_/github/sextractor_win/src/image.c.s
.PHONY : E_/github/sextractor_win/src/image.s

# target to generate assembly for a file
E_/github/sextractor_win/src/image.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/image.c.s
.PHONY : E_/github/sextractor_win/src/image.c.s

E_/github/sextractor_win/src/interpolate.obj: E_/github/sextractor_win/src/interpolate.c.obj
.PHONY : E_/github/sextractor_win/src/interpolate.obj

# target to build an object file
E_/github/sextractor_win/src/interpolate.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/interpolate.c.obj
.PHONY : E_/github/sextractor_win/src/interpolate.c.obj

E_/github/sextractor_win/src/interpolate.i: E_/github/sextractor_win/src/interpolate.c.i
.PHONY : E_/github/sextractor_win/src/interpolate.i

# target to preprocess a source file
E_/github/sextractor_win/src/interpolate.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/interpolate.c.i
.PHONY : E_/github/sextractor_win/src/interpolate.c.i

E_/github/sextractor_win/src/interpolate.s: E_/github/sextractor_win/src/interpolate.c.s
.PHONY : E_/github/sextractor_win/src/interpolate.s

# target to generate assembly for a file
E_/github/sextractor_win/src/interpolate.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/interpolate.c.s
.PHONY : E_/github/sextractor_win/src/interpolate.c.s

E_/github/sextractor_win/src/levmar/Axb.obj: E_/github/sextractor_win/src/levmar/Axb.c.obj
.PHONY : E_/github/sextractor_win/src/levmar/Axb.obj

# target to build an object file
E_/github/sextractor_win/src/levmar/Axb.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/Axb.c.obj
.PHONY : E_/github/sextractor_win/src/levmar/Axb.c.obj

E_/github/sextractor_win/src/levmar/Axb.i: E_/github/sextractor_win/src/levmar/Axb.c.i
.PHONY : E_/github/sextractor_win/src/levmar/Axb.i

# target to preprocess a source file
E_/github/sextractor_win/src/levmar/Axb.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/Axb.c.i
.PHONY : E_/github/sextractor_win/src/levmar/Axb.c.i

E_/github/sextractor_win/src/levmar/Axb.s: E_/github/sextractor_win/src/levmar/Axb.c.s
.PHONY : E_/github/sextractor_win/src/levmar/Axb.s

# target to generate assembly for a file
E_/github/sextractor_win/src/levmar/Axb.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/Axb.c.s
.PHONY : E_/github/sextractor_win/src/levmar/Axb.c.s

E_/github/sextractor_win/src/levmar/lm.obj: E_/github/sextractor_win/src/levmar/lm.c.obj
.PHONY : E_/github/sextractor_win/src/levmar/lm.obj

# target to build an object file
E_/github/sextractor_win/src/levmar/lm.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lm.c.obj
.PHONY : E_/github/sextractor_win/src/levmar/lm.c.obj

E_/github/sextractor_win/src/levmar/lm.i: E_/github/sextractor_win/src/levmar/lm.c.i
.PHONY : E_/github/sextractor_win/src/levmar/lm.i

# target to preprocess a source file
E_/github/sextractor_win/src/levmar/lm.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lm.c.i
.PHONY : E_/github/sextractor_win/src/levmar/lm.c.i

E_/github/sextractor_win/src/levmar/lm.s: E_/github/sextractor_win/src/levmar/lm.c.s
.PHONY : E_/github/sextractor_win/src/levmar/lm.s

# target to generate assembly for a file
E_/github/sextractor_win/src/levmar/lm.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lm.c.s
.PHONY : E_/github/sextractor_win/src/levmar/lm.c.s

E_/github/sextractor_win/src/levmar/lmbc.obj: E_/github/sextractor_win/src/levmar/lmbc.c.obj
.PHONY : E_/github/sextractor_win/src/levmar/lmbc.obj

# target to build an object file
E_/github/sextractor_win/src/levmar/lmbc.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbc.c.obj
.PHONY : E_/github/sextractor_win/src/levmar/lmbc.c.obj

E_/github/sextractor_win/src/levmar/lmbc.i: E_/github/sextractor_win/src/levmar/lmbc.c.i
.PHONY : E_/github/sextractor_win/src/levmar/lmbc.i

# target to preprocess a source file
E_/github/sextractor_win/src/levmar/lmbc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbc.c.i
.PHONY : E_/github/sextractor_win/src/levmar/lmbc.c.i

E_/github/sextractor_win/src/levmar/lmbc.s: E_/github/sextractor_win/src/levmar/lmbc.c.s
.PHONY : E_/github/sextractor_win/src/levmar/lmbc.s

# target to generate assembly for a file
E_/github/sextractor_win/src/levmar/lmbc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbc.c.s
.PHONY : E_/github/sextractor_win/src/levmar/lmbc.c.s

E_/github/sextractor_win/src/levmar/lmblec.obj: E_/github/sextractor_win/src/levmar/lmblec.c.obj
.PHONY : E_/github/sextractor_win/src/levmar/lmblec.obj

# target to build an object file
E_/github/sextractor_win/src/levmar/lmblec.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmblec.c.obj
.PHONY : E_/github/sextractor_win/src/levmar/lmblec.c.obj

E_/github/sextractor_win/src/levmar/lmblec.i: E_/github/sextractor_win/src/levmar/lmblec.c.i
.PHONY : E_/github/sextractor_win/src/levmar/lmblec.i

# target to preprocess a source file
E_/github/sextractor_win/src/levmar/lmblec.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmblec.c.i
.PHONY : E_/github/sextractor_win/src/levmar/lmblec.c.i

E_/github/sextractor_win/src/levmar/lmblec.s: E_/github/sextractor_win/src/levmar/lmblec.c.s
.PHONY : E_/github/sextractor_win/src/levmar/lmblec.s

# target to generate assembly for a file
E_/github/sextractor_win/src/levmar/lmblec.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmblec.c.s
.PHONY : E_/github/sextractor_win/src/levmar/lmblec.c.s

E_/github/sextractor_win/src/levmar/lmbleic.obj: E_/github/sextractor_win/src/levmar/lmbleic.c.obj
.PHONY : E_/github/sextractor_win/src/levmar/lmbleic.obj

# target to build an object file
E_/github/sextractor_win/src/levmar/lmbleic.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbleic.c.obj
.PHONY : E_/github/sextractor_win/src/levmar/lmbleic.c.obj

E_/github/sextractor_win/src/levmar/lmbleic.i: E_/github/sextractor_win/src/levmar/lmbleic.c.i
.PHONY : E_/github/sextractor_win/src/levmar/lmbleic.i

# target to preprocess a source file
E_/github/sextractor_win/src/levmar/lmbleic.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbleic.c.i
.PHONY : E_/github/sextractor_win/src/levmar/lmbleic.c.i

E_/github/sextractor_win/src/levmar/lmbleic.s: E_/github/sextractor_win/src/levmar/lmbleic.c.s
.PHONY : E_/github/sextractor_win/src/levmar/lmbleic.s

# target to generate assembly for a file
E_/github/sextractor_win/src/levmar/lmbleic.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmbleic.c.s
.PHONY : E_/github/sextractor_win/src/levmar/lmbleic.c.s

E_/github/sextractor_win/src/levmar/lmlec.obj: E_/github/sextractor_win/src/levmar/lmlec.c.obj
.PHONY : E_/github/sextractor_win/src/levmar/lmlec.obj

# target to build an object file
E_/github/sextractor_win/src/levmar/lmlec.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmlec.c.obj
.PHONY : E_/github/sextractor_win/src/levmar/lmlec.c.obj

E_/github/sextractor_win/src/levmar/lmlec.i: E_/github/sextractor_win/src/levmar/lmlec.c.i
.PHONY : E_/github/sextractor_win/src/levmar/lmlec.i

# target to preprocess a source file
E_/github/sextractor_win/src/levmar/lmlec.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmlec.c.i
.PHONY : E_/github/sextractor_win/src/levmar/lmlec.c.i

E_/github/sextractor_win/src/levmar/lmlec.s: E_/github/sextractor_win/src/levmar/lmlec.c.s
.PHONY : E_/github/sextractor_win/src/levmar/lmlec.s

# target to generate assembly for a file
E_/github/sextractor_win/src/levmar/lmlec.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/lmlec.c.s
.PHONY : E_/github/sextractor_win/src/levmar/lmlec.c.s

E_/github/sextractor_win/src/levmar/misc.obj: E_/github/sextractor_win/src/levmar/misc.c.obj
.PHONY : E_/github/sextractor_win/src/levmar/misc.obj

# target to build an object file
E_/github/sextractor_win/src/levmar/misc.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/misc.c.obj
.PHONY : E_/github/sextractor_win/src/levmar/misc.c.obj

E_/github/sextractor_win/src/levmar/misc.i: E_/github/sextractor_win/src/levmar/misc.c.i
.PHONY : E_/github/sextractor_win/src/levmar/misc.i

# target to preprocess a source file
E_/github/sextractor_win/src/levmar/misc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/misc.c.i
.PHONY : E_/github/sextractor_win/src/levmar/misc.c.i

E_/github/sextractor_win/src/levmar/misc.s: E_/github/sextractor_win/src/levmar/misc.c.s
.PHONY : E_/github/sextractor_win/src/levmar/misc.s

# target to generate assembly for a file
E_/github/sextractor_win/src/levmar/misc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/E_/github/sextractor_win/src/levmar/misc.c.s
.PHONY : E_/github/sextractor_win/src/levmar/misc.c.s

E_/github/sextractor_win/src/main.obj: E_/github/sextractor_win/src/main.c.obj
.PHONY : E_/github/sextractor_win/src/main.obj

# target to build an object file
E_/github/sextractor_win/src/main.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/main.c.obj
.PHONY : E_/github/sextractor_win/src/main.c.obj

E_/github/sextractor_win/src/main.i: E_/github/sextractor_win/src/main.c.i
.PHONY : E_/github/sextractor_win/src/main.i

# target to preprocess a source file
E_/github/sextractor_win/src/main.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/main.c.i
.PHONY : E_/github/sextractor_win/src/main.c.i

E_/github/sextractor_win/src/main.s: E_/github/sextractor_win/src/main.c.s
.PHONY : E_/github/sextractor_win/src/main.s

# target to generate assembly for a file
E_/github/sextractor_win/src/main.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/main.c.s
.PHONY : E_/github/sextractor_win/src/main.c.s

E_/github/sextractor_win/src/makeit.obj: E_/github/sextractor_win/src/makeit.c.obj
.PHONY : E_/github/sextractor_win/src/makeit.obj

# target to build an object file
E_/github/sextractor_win/src/makeit.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/makeit.c.obj
.PHONY : E_/github/sextractor_win/src/makeit.c.obj

E_/github/sextractor_win/src/makeit.i: E_/github/sextractor_win/src/makeit.c.i
.PHONY : E_/github/sextractor_win/src/makeit.i

# target to preprocess a source file
E_/github/sextractor_win/src/makeit.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/makeit.c.i
.PHONY : E_/github/sextractor_win/src/makeit.c.i

E_/github/sextractor_win/src/makeit.s: E_/github/sextractor_win/src/makeit.c.s
.PHONY : E_/github/sextractor_win/src/makeit.s

# target to generate assembly for a file
E_/github/sextractor_win/src/makeit.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/makeit.c.s
.PHONY : E_/github/sextractor_win/src/makeit.c.s

E_/github/sextractor_win/src/manobjlist.obj: E_/github/sextractor_win/src/manobjlist.c.obj
.PHONY : E_/github/sextractor_win/src/manobjlist.obj

# target to build an object file
E_/github/sextractor_win/src/manobjlist.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/manobjlist.c.obj
.PHONY : E_/github/sextractor_win/src/manobjlist.c.obj

E_/github/sextractor_win/src/manobjlist.i: E_/github/sextractor_win/src/manobjlist.c.i
.PHONY : E_/github/sextractor_win/src/manobjlist.i

# target to preprocess a source file
E_/github/sextractor_win/src/manobjlist.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/manobjlist.c.i
.PHONY : E_/github/sextractor_win/src/manobjlist.c.i

E_/github/sextractor_win/src/manobjlist.s: E_/github/sextractor_win/src/manobjlist.c.s
.PHONY : E_/github/sextractor_win/src/manobjlist.s

# target to generate assembly for a file
E_/github/sextractor_win/src/manobjlist.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/manobjlist.c.s
.PHONY : E_/github/sextractor_win/src/manobjlist.c.s

E_/github/sextractor_win/src/misc.obj: E_/github/sextractor_win/src/misc.c.obj
.PHONY : E_/github/sextractor_win/src/misc.obj

# target to build an object file
E_/github/sextractor_win/src/misc.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/misc.c.obj
.PHONY : E_/github/sextractor_win/src/misc.c.obj

E_/github/sextractor_win/src/misc.i: E_/github/sextractor_win/src/misc.c.i
.PHONY : E_/github/sextractor_win/src/misc.i

# target to preprocess a source file
E_/github/sextractor_win/src/misc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/misc.c.i
.PHONY : E_/github/sextractor_win/src/misc.c.i

E_/github/sextractor_win/src/misc.s: E_/github/sextractor_win/src/misc.c.s
.PHONY : E_/github/sextractor_win/src/misc.s

# target to generate assembly for a file
E_/github/sextractor_win/src/misc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/misc.c.s
.PHONY : E_/github/sextractor_win/src/misc.c.s

E_/github/sextractor_win/src/neurro.obj: E_/github/sextractor_win/src/neurro.c.obj
.PHONY : E_/github/sextractor_win/src/neurro.obj

# target to build an object file
E_/github/sextractor_win/src/neurro.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/neurro.c.obj
.PHONY : E_/github/sextractor_win/src/neurro.c.obj

E_/github/sextractor_win/src/neurro.i: E_/github/sextractor_win/src/neurro.c.i
.PHONY : E_/github/sextractor_win/src/neurro.i

# target to preprocess a source file
E_/github/sextractor_win/src/neurro.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/neurro.c.i
.PHONY : E_/github/sextractor_win/src/neurro.c.i

E_/github/sextractor_win/src/neurro.s: E_/github/sextractor_win/src/neurro.c.s
.PHONY : E_/github/sextractor_win/src/neurro.s

# target to generate assembly for a file
E_/github/sextractor_win/src/neurro.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/neurro.c.s
.PHONY : E_/github/sextractor_win/src/neurro.c.s

E_/github/sextractor_win/src/pattern.obj: E_/github/sextractor_win/src/pattern.c.obj
.PHONY : E_/github/sextractor_win/src/pattern.obj

# target to build an object file
E_/github/sextractor_win/src/pattern.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/pattern.c.obj
.PHONY : E_/github/sextractor_win/src/pattern.c.obj

E_/github/sextractor_win/src/pattern.i: E_/github/sextractor_win/src/pattern.c.i
.PHONY : E_/github/sextractor_win/src/pattern.i

# target to preprocess a source file
E_/github/sextractor_win/src/pattern.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/pattern.c.i
.PHONY : E_/github/sextractor_win/src/pattern.c.i

E_/github/sextractor_win/src/pattern.s: E_/github/sextractor_win/src/pattern.c.s
.PHONY : E_/github/sextractor_win/src/pattern.s

# target to generate assembly for a file
E_/github/sextractor_win/src/pattern.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/pattern.c.s
.PHONY : E_/github/sextractor_win/src/pattern.c.s

E_/github/sextractor_win/src/pc.obj: E_/github/sextractor_win/src/pc.c.obj
.PHONY : E_/github/sextractor_win/src/pc.obj

# target to build an object file
E_/github/sextractor_win/src/pc.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/pc.c.obj
.PHONY : E_/github/sextractor_win/src/pc.c.obj

E_/github/sextractor_win/src/pc.i: E_/github/sextractor_win/src/pc.c.i
.PHONY : E_/github/sextractor_win/src/pc.i

# target to preprocess a source file
E_/github/sextractor_win/src/pc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/pc.c.i
.PHONY : E_/github/sextractor_win/src/pc.c.i

E_/github/sextractor_win/src/pc.s: E_/github/sextractor_win/src/pc.c.s
.PHONY : E_/github/sextractor_win/src/pc.s

# target to generate assembly for a file
E_/github/sextractor_win/src/pc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/pc.c.s
.PHONY : E_/github/sextractor_win/src/pc.c.s

E_/github/sextractor_win/src/photom.obj: E_/github/sextractor_win/src/photom.c.obj
.PHONY : E_/github/sextractor_win/src/photom.obj

# target to build an object file
E_/github/sextractor_win/src/photom.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/photom.c.obj
.PHONY : E_/github/sextractor_win/src/photom.c.obj

E_/github/sextractor_win/src/photom.i: E_/github/sextractor_win/src/photom.c.i
.PHONY : E_/github/sextractor_win/src/photom.i

# target to preprocess a source file
E_/github/sextractor_win/src/photom.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/photom.c.i
.PHONY : E_/github/sextractor_win/src/photom.c.i

E_/github/sextractor_win/src/photom.s: E_/github/sextractor_win/src/photom.c.s
.PHONY : E_/github/sextractor_win/src/photom.s

# target to generate assembly for a file
E_/github/sextractor_win/src/photom.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/photom.c.s
.PHONY : E_/github/sextractor_win/src/photom.c.s

E_/github/sextractor_win/src/plist.obj: E_/github/sextractor_win/src/plist.c.obj
.PHONY : E_/github/sextractor_win/src/plist.obj

# target to build an object file
E_/github/sextractor_win/src/plist.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/plist.c.obj
.PHONY : E_/github/sextractor_win/src/plist.c.obj

E_/github/sextractor_win/src/plist.i: E_/github/sextractor_win/src/plist.c.i
.PHONY : E_/github/sextractor_win/src/plist.i

# target to preprocess a source file
E_/github/sextractor_win/src/plist.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/plist.c.i
.PHONY : E_/github/sextractor_win/src/plist.c.i

E_/github/sextractor_win/src/plist.s: E_/github/sextractor_win/src/plist.c.s
.PHONY : E_/github/sextractor_win/src/plist.s

# target to generate assembly for a file
E_/github/sextractor_win/src/plist.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/plist.c.s
.PHONY : E_/github/sextractor_win/src/plist.c.s

E_/github/sextractor_win/src/prefs.obj: E_/github/sextractor_win/src/prefs.c.obj
.PHONY : E_/github/sextractor_win/src/prefs.obj

# target to build an object file
E_/github/sextractor_win/src/prefs.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/prefs.c.obj
.PHONY : E_/github/sextractor_win/src/prefs.c.obj

E_/github/sextractor_win/src/prefs.i: E_/github/sextractor_win/src/prefs.c.i
.PHONY : E_/github/sextractor_win/src/prefs.i

# target to preprocess a source file
E_/github/sextractor_win/src/prefs.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/prefs.c.i
.PHONY : E_/github/sextractor_win/src/prefs.c.i

E_/github/sextractor_win/src/prefs.s: E_/github/sextractor_win/src/prefs.c.s
.PHONY : E_/github/sextractor_win/src/prefs.s

# target to generate assembly for a file
E_/github/sextractor_win/src/prefs.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/prefs.c.s
.PHONY : E_/github/sextractor_win/src/prefs.c.s

E_/github/sextractor_win/src/profit.obj: E_/github/sextractor_win/src/profit.c.obj
.PHONY : E_/github/sextractor_win/src/profit.obj

# target to build an object file
E_/github/sextractor_win/src/profit.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/profit.c.obj
.PHONY : E_/github/sextractor_win/src/profit.c.obj

E_/github/sextractor_win/src/profit.i: E_/github/sextractor_win/src/profit.c.i
.PHONY : E_/github/sextractor_win/src/profit.i

# target to preprocess a source file
E_/github/sextractor_win/src/profit.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/profit.c.i
.PHONY : E_/github/sextractor_win/src/profit.c.i

E_/github/sextractor_win/src/profit.s: E_/github/sextractor_win/src/profit.c.s
.PHONY : E_/github/sextractor_win/src/profit.s

# target to generate assembly for a file
E_/github/sextractor_win/src/profit.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/profit.c.s
.PHONY : E_/github/sextractor_win/src/profit.c.s

E_/github/sextractor_win/src/psf.obj: E_/github/sextractor_win/src/psf.c.obj
.PHONY : E_/github/sextractor_win/src/psf.obj

# target to build an object file
E_/github/sextractor_win/src/psf.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/psf.c.obj
.PHONY : E_/github/sextractor_win/src/psf.c.obj

E_/github/sextractor_win/src/psf.i: E_/github/sextractor_win/src/psf.c.i
.PHONY : E_/github/sextractor_win/src/psf.i

# target to preprocess a source file
E_/github/sextractor_win/src/psf.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/psf.c.i
.PHONY : E_/github/sextractor_win/src/psf.c.i

E_/github/sextractor_win/src/psf.s: E_/github/sextractor_win/src/psf.c.s
.PHONY : E_/github/sextractor_win/src/psf.s

# target to generate assembly for a file
E_/github/sextractor_win/src/psf.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/psf.c.s
.PHONY : E_/github/sextractor_win/src/psf.c.s

E_/github/sextractor_win/src/readimage.obj: E_/github/sextractor_win/src/readimage.c.obj
.PHONY : E_/github/sextractor_win/src/readimage.obj

# target to build an object file
E_/github/sextractor_win/src/readimage.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/readimage.c.obj
.PHONY : E_/github/sextractor_win/src/readimage.c.obj

E_/github/sextractor_win/src/readimage.i: E_/github/sextractor_win/src/readimage.c.i
.PHONY : E_/github/sextractor_win/src/readimage.i

# target to preprocess a source file
E_/github/sextractor_win/src/readimage.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/readimage.c.i
.PHONY : E_/github/sextractor_win/src/readimage.c.i

E_/github/sextractor_win/src/readimage.s: E_/github/sextractor_win/src/readimage.c.s
.PHONY : E_/github/sextractor_win/src/readimage.s

# target to generate assembly for a file
E_/github/sextractor_win/src/readimage.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/readimage.c.s
.PHONY : E_/github/sextractor_win/src/readimage.c.s

E_/github/sextractor_win/src/refine.obj: E_/github/sextractor_win/src/refine.c.obj
.PHONY : E_/github/sextractor_win/src/refine.obj

# target to build an object file
E_/github/sextractor_win/src/refine.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/refine.c.obj
.PHONY : E_/github/sextractor_win/src/refine.c.obj

E_/github/sextractor_win/src/refine.i: E_/github/sextractor_win/src/refine.c.i
.PHONY : E_/github/sextractor_win/src/refine.i

# target to preprocess a source file
E_/github/sextractor_win/src/refine.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/refine.c.i
.PHONY : E_/github/sextractor_win/src/refine.c.i

E_/github/sextractor_win/src/refine.s: E_/github/sextractor_win/src/refine.c.s
.PHONY : E_/github/sextractor_win/src/refine.s

# target to generate assembly for a file
E_/github/sextractor_win/src/refine.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/refine.c.s
.PHONY : E_/github/sextractor_win/src/refine.c.s

E_/github/sextractor_win/src/retina.obj: E_/github/sextractor_win/src/retina.c.obj
.PHONY : E_/github/sextractor_win/src/retina.obj

# target to build an object file
E_/github/sextractor_win/src/retina.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/retina.c.obj
.PHONY : E_/github/sextractor_win/src/retina.c.obj

E_/github/sextractor_win/src/retina.i: E_/github/sextractor_win/src/retina.c.i
.PHONY : E_/github/sextractor_win/src/retina.i

# target to preprocess a source file
E_/github/sextractor_win/src/retina.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/retina.c.i
.PHONY : E_/github/sextractor_win/src/retina.c.i

E_/github/sextractor_win/src/retina.s: E_/github/sextractor_win/src/retina.c.s
.PHONY : E_/github/sextractor_win/src/retina.s

# target to generate assembly for a file
E_/github/sextractor_win/src/retina.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/retina.c.s
.PHONY : E_/github/sextractor_win/src/retina.c.s

E_/github/sextractor_win/src/scan.obj: E_/github/sextractor_win/src/scan.c.obj
.PHONY : E_/github/sextractor_win/src/scan.obj

# target to build an object file
E_/github/sextractor_win/src/scan.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/scan.c.obj
.PHONY : E_/github/sextractor_win/src/scan.c.obj

E_/github/sextractor_win/src/scan.i: E_/github/sextractor_win/src/scan.c.i
.PHONY : E_/github/sextractor_win/src/scan.i

# target to preprocess a source file
E_/github/sextractor_win/src/scan.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/scan.c.i
.PHONY : E_/github/sextractor_win/src/scan.c.i

E_/github/sextractor_win/src/scan.s: E_/github/sextractor_win/src/scan.c.s
.PHONY : E_/github/sextractor_win/src/scan.s

# target to generate assembly for a file
E_/github/sextractor_win/src/scan.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/scan.c.s
.PHONY : E_/github/sextractor_win/src/scan.c.s

E_/github/sextractor_win/src/som.obj: E_/github/sextractor_win/src/som.c.obj
.PHONY : E_/github/sextractor_win/src/som.obj

# target to build an object file
E_/github/sextractor_win/src/som.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/som.c.obj
.PHONY : E_/github/sextractor_win/src/som.c.obj

E_/github/sextractor_win/src/som.i: E_/github/sextractor_win/src/som.c.i
.PHONY : E_/github/sextractor_win/src/som.i

# target to preprocess a source file
E_/github/sextractor_win/src/som.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/som.c.i
.PHONY : E_/github/sextractor_win/src/som.c.i

E_/github/sextractor_win/src/som.s: E_/github/sextractor_win/src/som.c.s
.PHONY : E_/github/sextractor_win/src/som.s

# target to generate assembly for a file
E_/github/sextractor_win/src/som.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/som.c.s
.PHONY : E_/github/sextractor_win/src/som.c.s

E_/github/sextractor_win/src/wcs/cel.obj: E_/github/sextractor_win/src/wcs/cel.c.obj
.PHONY : E_/github/sextractor_win/src/wcs/cel.obj

# target to build an object file
E_/github/sextractor_win/src/wcs/cel.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/cel.c.obj
.PHONY : E_/github/sextractor_win/src/wcs/cel.c.obj

E_/github/sextractor_win/src/wcs/cel.i: E_/github/sextractor_win/src/wcs/cel.c.i
.PHONY : E_/github/sextractor_win/src/wcs/cel.i

# target to preprocess a source file
E_/github/sextractor_win/src/wcs/cel.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/cel.c.i
.PHONY : E_/github/sextractor_win/src/wcs/cel.c.i

E_/github/sextractor_win/src/wcs/cel.s: E_/github/sextractor_win/src/wcs/cel.c.s
.PHONY : E_/github/sextractor_win/src/wcs/cel.s

# target to generate assembly for a file
E_/github/sextractor_win/src/wcs/cel.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/cel.c.s
.PHONY : E_/github/sextractor_win/src/wcs/cel.c.s

E_/github/sextractor_win/src/wcs/lin.obj: E_/github/sextractor_win/src/wcs/lin.c.obj
.PHONY : E_/github/sextractor_win/src/wcs/lin.obj

# target to build an object file
E_/github/sextractor_win/src/wcs/lin.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/lin.c.obj
.PHONY : E_/github/sextractor_win/src/wcs/lin.c.obj

E_/github/sextractor_win/src/wcs/lin.i: E_/github/sextractor_win/src/wcs/lin.c.i
.PHONY : E_/github/sextractor_win/src/wcs/lin.i

# target to preprocess a source file
E_/github/sextractor_win/src/wcs/lin.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/lin.c.i
.PHONY : E_/github/sextractor_win/src/wcs/lin.c.i

E_/github/sextractor_win/src/wcs/lin.s: E_/github/sextractor_win/src/wcs/lin.c.s
.PHONY : E_/github/sextractor_win/src/wcs/lin.s

# target to generate assembly for a file
E_/github/sextractor_win/src/wcs/lin.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/lin.c.s
.PHONY : E_/github/sextractor_win/src/wcs/lin.c.s

E_/github/sextractor_win/src/wcs/poly.obj: E_/github/sextractor_win/src/wcs/poly.c.obj
.PHONY : E_/github/sextractor_win/src/wcs/poly.obj

# target to build an object file
E_/github/sextractor_win/src/wcs/poly.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/poly.c.obj
.PHONY : E_/github/sextractor_win/src/wcs/poly.c.obj

E_/github/sextractor_win/src/wcs/poly.i: E_/github/sextractor_win/src/wcs/poly.c.i
.PHONY : E_/github/sextractor_win/src/wcs/poly.i

# target to preprocess a source file
E_/github/sextractor_win/src/wcs/poly.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/poly.c.i
.PHONY : E_/github/sextractor_win/src/wcs/poly.c.i

E_/github/sextractor_win/src/wcs/poly.s: E_/github/sextractor_win/src/wcs/poly.c.s
.PHONY : E_/github/sextractor_win/src/wcs/poly.s

# target to generate assembly for a file
E_/github/sextractor_win/src/wcs/poly.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/poly.c.s
.PHONY : E_/github/sextractor_win/src/wcs/poly.c.s

E_/github/sextractor_win/src/wcs/proj.obj: E_/github/sextractor_win/src/wcs/proj.c.obj
.PHONY : E_/github/sextractor_win/src/wcs/proj.obj

# target to build an object file
E_/github/sextractor_win/src/wcs/proj.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/proj.c.obj
.PHONY : E_/github/sextractor_win/src/wcs/proj.c.obj

E_/github/sextractor_win/src/wcs/proj.i: E_/github/sextractor_win/src/wcs/proj.c.i
.PHONY : E_/github/sextractor_win/src/wcs/proj.i

# target to preprocess a source file
E_/github/sextractor_win/src/wcs/proj.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/proj.c.i
.PHONY : E_/github/sextractor_win/src/wcs/proj.c.i

E_/github/sextractor_win/src/wcs/proj.s: E_/github/sextractor_win/src/wcs/proj.c.s
.PHONY : E_/github/sextractor_win/src/wcs/proj.s

# target to generate assembly for a file
E_/github/sextractor_win/src/wcs/proj.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/proj.c.s
.PHONY : E_/github/sextractor_win/src/wcs/proj.c.s

E_/github/sextractor_win/src/wcs/sph.obj: E_/github/sextractor_win/src/wcs/sph.c.obj
.PHONY : E_/github/sextractor_win/src/wcs/sph.obj

# target to build an object file
E_/github/sextractor_win/src/wcs/sph.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/sph.c.obj
.PHONY : E_/github/sextractor_win/src/wcs/sph.c.obj

E_/github/sextractor_win/src/wcs/sph.i: E_/github/sextractor_win/src/wcs/sph.c.i
.PHONY : E_/github/sextractor_win/src/wcs/sph.i

# target to preprocess a source file
E_/github/sextractor_win/src/wcs/sph.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/sph.c.i
.PHONY : E_/github/sextractor_win/src/wcs/sph.c.i

E_/github/sextractor_win/src/wcs/sph.s: E_/github/sextractor_win/src/wcs/sph.c.s
.PHONY : E_/github/sextractor_win/src/wcs/sph.s

# target to generate assembly for a file
E_/github/sextractor_win/src/wcs/sph.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/sph.c.s
.PHONY : E_/github/sextractor_win/src/wcs/sph.c.s

E_/github/sextractor_win/src/wcs/tnx.obj: E_/github/sextractor_win/src/wcs/tnx.c.obj
.PHONY : E_/github/sextractor_win/src/wcs/tnx.obj

# target to build an object file
E_/github/sextractor_win/src/wcs/tnx.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/tnx.c.obj
.PHONY : E_/github/sextractor_win/src/wcs/tnx.c.obj

E_/github/sextractor_win/src/wcs/tnx.i: E_/github/sextractor_win/src/wcs/tnx.c.i
.PHONY : E_/github/sextractor_win/src/wcs/tnx.i

# target to preprocess a source file
E_/github/sextractor_win/src/wcs/tnx.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/tnx.c.i
.PHONY : E_/github/sextractor_win/src/wcs/tnx.c.i

E_/github/sextractor_win/src/wcs/tnx.s: E_/github/sextractor_win/src/wcs/tnx.c.s
.PHONY : E_/github/sextractor_win/src/wcs/tnx.s

# target to generate assembly for a file
E_/github/sextractor_win/src/wcs/tnx.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/tnx.c.s
.PHONY : E_/github/sextractor_win/src/wcs/tnx.c.s

E_/github/sextractor_win/src/wcs/wcs.obj: E_/github/sextractor_win/src/wcs/wcs.c.obj
.PHONY : E_/github/sextractor_win/src/wcs/wcs.obj

# target to build an object file
E_/github/sextractor_win/src/wcs/wcs.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcs.c.obj
.PHONY : E_/github/sextractor_win/src/wcs/wcs.c.obj

E_/github/sextractor_win/src/wcs/wcs.i: E_/github/sextractor_win/src/wcs/wcs.c.i
.PHONY : E_/github/sextractor_win/src/wcs/wcs.i

# target to preprocess a source file
E_/github/sextractor_win/src/wcs/wcs.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcs.c.i
.PHONY : E_/github/sextractor_win/src/wcs/wcs.c.i

E_/github/sextractor_win/src/wcs/wcs.s: E_/github/sextractor_win/src/wcs/wcs.c.s
.PHONY : E_/github/sextractor_win/src/wcs/wcs.s

# target to generate assembly for a file
E_/github/sextractor_win/src/wcs/wcs.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcs.c.s
.PHONY : E_/github/sextractor_win/src/wcs/wcs.c.s

E_/github/sextractor_win/src/wcs/wcstrig.obj: E_/github/sextractor_win/src/wcs/wcstrig.c.obj
.PHONY : E_/github/sextractor_win/src/wcs/wcstrig.obj

# target to build an object file
E_/github/sextractor_win/src/wcs/wcstrig.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcstrig.c.obj
.PHONY : E_/github/sextractor_win/src/wcs/wcstrig.c.obj

E_/github/sextractor_win/src/wcs/wcstrig.i: E_/github/sextractor_win/src/wcs/wcstrig.c.i
.PHONY : E_/github/sextractor_win/src/wcs/wcstrig.i

# target to preprocess a source file
E_/github/sextractor_win/src/wcs/wcstrig.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcstrig.c.i
.PHONY : E_/github/sextractor_win/src/wcs/wcstrig.c.i

E_/github/sextractor_win/src/wcs/wcstrig.s: E_/github/sextractor_win/src/wcs/wcstrig.c.s
.PHONY : E_/github/sextractor_win/src/wcs/wcstrig.s

# target to generate assembly for a file
E_/github/sextractor_win/src/wcs/wcstrig.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcstrig.c.s
.PHONY : E_/github/sextractor_win/src/wcs/wcstrig.c.s

E_/github/sextractor_win/src/weight.obj: E_/github/sextractor_win/src/weight.c.obj
.PHONY : E_/github/sextractor_win/src/weight.obj

# target to build an object file
E_/github/sextractor_win/src/weight.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/weight.c.obj
.PHONY : E_/github/sextractor_win/src/weight.c.obj

E_/github/sextractor_win/src/weight.i: E_/github/sextractor_win/src/weight.c.i
.PHONY : E_/github/sextractor_win/src/weight.i

# target to preprocess a source file
E_/github/sextractor_win/src/weight.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/weight.c.i
.PHONY : E_/github/sextractor_win/src/weight.c.i

E_/github/sextractor_win/src/weight.s: E_/github/sextractor_win/src/weight.c.s
.PHONY : E_/github/sextractor_win/src/weight.s

# target to generate assembly for a file
E_/github/sextractor_win/src/weight.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/weight.c.s
.PHONY : E_/github/sextractor_win/src/weight.c.s

E_/github/sextractor_win/src/winpos.obj: E_/github/sextractor_win/src/winpos.c.obj
.PHONY : E_/github/sextractor_win/src/winpos.obj

# target to build an object file
E_/github/sextractor_win/src/winpos.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/winpos.c.obj
.PHONY : E_/github/sextractor_win/src/winpos.c.obj

E_/github/sextractor_win/src/winpos.i: E_/github/sextractor_win/src/winpos.c.i
.PHONY : E_/github/sextractor_win/src/winpos.i

# target to preprocess a source file
E_/github/sextractor_win/src/winpos.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/winpos.c.i
.PHONY : E_/github/sextractor_win/src/winpos.c.i

E_/github/sextractor_win/src/winpos.s: E_/github/sextractor_win/src/winpos.c.s
.PHONY : E_/github/sextractor_win/src/winpos.s

# target to generate assembly for a file
E_/github/sextractor_win/src/winpos.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/winpos.c.s
.PHONY : E_/github/sextractor_win/src/winpos.c.s

E_/github/sextractor_win/src/xml.obj: E_/github/sextractor_win/src/xml.c.obj
.PHONY : E_/github/sextractor_win/src/xml.obj

# target to build an object file
E_/github/sextractor_win/src/xml.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/xml.c.obj
.PHONY : E_/github/sextractor_win/src/xml.c.obj

E_/github/sextractor_win/src/xml.i: E_/github/sextractor_win/src/xml.c.i
.PHONY : E_/github/sextractor_win/src/xml.i

# target to preprocess a source file
E_/github/sextractor_win/src/xml.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/xml.c.i
.PHONY : E_/github/sextractor_win/src/xml.c.i

E_/github/sextractor_win/src/xml.s: E_/github/sextractor_win/src/xml.c.s
.PHONY : E_/github/sextractor_win/src/xml.s

# target to generate assembly for a file
E_/github/sextractor_win/src/xml.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/E_/github/sextractor_win/src/xml.c.s
.PHONY : E_/github/sextractor_win/src/xml.c.s

windows_stubs.obj: windows_stubs.c.obj
.PHONY : windows_stubs.obj

# target to build an object file
windows_stubs.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/windows_stubs.c.obj
.PHONY : windows_stubs.c.obj

windows_stubs.i: windows_stubs.c.i
.PHONY : windows_stubs.i

# target to preprocess a source file
windows_stubs.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/windows_stubs.c.i
.PHONY : windows_stubs.c.i

windows_stubs.s: windows_stubs.c.s
.PHONY : windows_stubs.s

# target to generate assembly for a file
windows_stubs.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/windows_stubs.c.s
.PHONY : windows_stubs.c.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... install
	@echo ... install/local
	@echo ... install/strip
	@echo ... list_install_components
	@echo ... rebuild_cache
	@echo ... fits
	@echo ... levmar
	@echo ... sex
	@echo ... wcs
	@echo ... E_/github/sextractor_win/src/analyse.obj
	@echo ... E_/github/sextractor_win/src/analyse.i
	@echo ... E_/github/sextractor_win/src/analyse.s
	@echo ... E_/github/sextractor_win/src/assoc.obj
	@echo ... E_/github/sextractor_win/src/assoc.i
	@echo ... E_/github/sextractor_win/src/assoc.s
	@echo ... E_/github/sextractor_win/src/astrom.obj
	@echo ... E_/github/sextractor_win/src/astrom.i
	@echo ... E_/github/sextractor_win/src/astrom.s
	@echo ... E_/github/sextractor_win/src/back.obj
	@echo ... E_/github/sextractor_win/src/back.i
	@echo ... E_/github/sextractor_win/src/back.s
	@echo ... E_/github/sextractor_win/src/bpro.obj
	@echo ... E_/github/sextractor_win/src/bpro.i
	@echo ... E_/github/sextractor_win/src/bpro.s
	@echo ... E_/github/sextractor_win/src/catout.obj
	@echo ... E_/github/sextractor_win/src/catout.i
	@echo ... E_/github/sextractor_win/src/catout.s
	@echo ... E_/github/sextractor_win/src/check.obj
	@echo ... E_/github/sextractor_win/src/check.i
	@echo ... E_/github/sextractor_win/src/check.s
	@echo ... E_/github/sextractor_win/src/clean.obj
	@echo ... E_/github/sextractor_win/src/clean.i
	@echo ... E_/github/sextractor_win/src/clean.s
	@echo ... E_/github/sextractor_win/src/dgeo.obj
	@echo ... E_/github/sextractor_win/src/dgeo.i
	@echo ... E_/github/sextractor_win/src/dgeo.s
	@echo ... E_/github/sextractor_win/src/extract.obj
	@echo ... E_/github/sextractor_win/src/extract.i
	@echo ... E_/github/sextractor_win/src/extract.s
	@echo ... E_/github/sextractor_win/src/fft.obj
	@echo ... E_/github/sextractor_win/src/fft.i
	@echo ... E_/github/sextractor_win/src/fft.s
	@echo ... E_/github/sextractor_win/src/field.obj
	@echo ... E_/github/sextractor_win/src/field.i
	@echo ... E_/github/sextractor_win/src/field.s
	@echo ... E_/github/sextractor_win/src/filter.obj
	@echo ... E_/github/sextractor_win/src/filter.i
	@echo ... E_/github/sextractor_win/src/filter.s
	@echo ... E_/github/sextractor_win/src/fits/fitsbody.obj
	@echo ... E_/github/sextractor_win/src/fits/fitsbody.i
	@echo ... E_/github/sextractor_win/src/fits/fitsbody.s
	@echo ... E_/github/sextractor_win/src/fits/fitscat.obj
	@echo ... E_/github/sextractor_win/src/fits/fitscat.i
	@echo ... E_/github/sextractor_win/src/fits/fitscat.s
	@echo ... E_/github/sextractor_win/src/fits/fitscheck.obj
	@echo ... E_/github/sextractor_win/src/fits/fitscheck.i
	@echo ... E_/github/sextractor_win/src/fits/fitscheck.s
	@echo ... E_/github/sextractor_win/src/fits/fitscleanup.obj
	@echo ... E_/github/sextractor_win/src/fits/fitscleanup.i
	@echo ... E_/github/sextractor_win/src/fits/fitscleanup.s
	@echo ... E_/github/sextractor_win/src/fits/fitsconv.obj
	@echo ... E_/github/sextractor_win/src/fits/fitsconv.i
	@echo ... E_/github/sextractor_win/src/fits/fitsconv.s
	@echo ... E_/github/sextractor_win/src/fits/fitshead.obj
	@echo ... E_/github/sextractor_win/src/fits/fitshead.i
	@echo ... E_/github/sextractor_win/src/fits/fitshead.s
	@echo ... E_/github/sextractor_win/src/fits/fitskey.obj
	@echo ... E_/github/sextractor_win/src/fits/fitskey.i
	@echo ... E_/github/sextractor_win/src/fits/fitskey.s
	@echo ... E_/github/sextractor_win/src/fits/fitsmisc.obj
	@echo ... E_/github/sextractor_win/src/fits/fitsmisc.i
	@echo ... E_/github/sextractor_win/src/fits/fitsmisc.s
	@echo ... E_/github/sextractor_win/src/fits/fitsread.obj
	@echo ... E_/github/sextractor_win/src/fits/fitsread.i
	@echo ... E_/github/sextractor_win/src/fits/fitsread.s
	@echo ... E_/github/sextractor_win/src/fits/fitstab.obj
	@echo ... E_/github/sextractor_win/src/fits/fitstab.i
	@echo ... E_/github/sextractor_win/src/fits/fitstab.s
	@echo ... E_/github/sextractor_win/src/fits/fitsutil.obj
	@echo ... E_/github/sextractor_win/src/fits/fitsutil.i
	@echo ... E_/github/sextractor_win/src/fits/fitsutil.s
	@echo ... E_/github/sextractor_win/src/fits/fitswrite.obj
	@echo ... E_/github/sextractor_win/src/fits/fitswrite.i
	@echo ... E_/github/sextractor_win/src/fits/fitswrite.s
	@echo ... E_/github/sextractor_win/src/fitswcs.obj
	@echo ... E_/github/sextractor_win/src/fitswcs.i
	@echo ... E_/github/sextractor_win/src/fitswcs.s
	@echo ... E_/github/sextractor_win/src/flag.obj
	@echo ... E_/github/sextractor_win/src/flag.i
	@echo ... E_/github/sextractor_win/src/flag.s
	@echo ... E_/github/sextractor_win/src/graph.obj
	@echo ... E_/github/sextractor_win/src/graph.i
	@echo ... E_/github/sextractor_win/src/graph.s
	@echo ... E_/github/sextractor_win/src/growth.obj
	@echo ... E_/github/sextractor_win/src/growth.i
	@echo ... E_/github/sextractor_win/src/growth.s
	@echo ... E_/github/sextractor_win/src/header.obj
	@echo ... E_/github/sextractor_win/src/header.i
	@echo ... E_/github/sextractor_win/src/header.s
	@echo ... E_/github/sextractor_win/src/image.obj
	@echo ... E_/github/sextractor_win/src/image.i
	@echo ... E_/github/sextractor_win/src/image.s
	@echo ... E_/github/sextractor_win/src/interpolate.obj
	@echo ... E_/github/sextractor_win/src/interpolate.i
	@echo ... E_/github/sextractor_win/src/interpolate.s
	@echo ... E_/github/sextractor_win/src/levmar/Axb.obj
	@echo ... E_/github/sextractor_win/src/levmar/Axb.i
	@echo ... E_/github/sextractor_win/src/levmar/Axb.s
	@echo ... E_/github/sextractor_win/src/levmar/lm.obj
	@echo ... E_/github/sextractor_win/src/levmar/lm.i
	@echo ... E_/github/sextractor_win/src/levmar/lm.s
	@echo ... E_/github/sextractor_win/src/levmar/lmbc.obj
	@echo ... E_/github/sextractor_win/src/levmar/lmbc.i
	@echo ... E_/github/sextractor_win/src/levmar/lmbc.s
	@echo ... E_/github/sextractor_win/src/levmar/lmblec.obj
	@echo ... E_/github/sextractor_win/src/levmar/lmblec.i
	@echo ... E_/github/sextractor_win/src/levmar/lmblec.s
	@echo ... E_/github/sextractor_win/src/levmar/lmbleic.obj
	@echo ... E_/github/sextractor_win/src/levmar/lmbleic.i
	@echo ... E_/github/sextractor_win/src/levmar/lmbleic.s
	@echo ... E_/github/sextractor_win/src/levmar/lmlec.obj
	@echo ... E_/github/sextractor_win/src/levmar/lmlec.i
	@echo ... E_/github/sextractor_win/src/levmar/lmlec.s
	@echo ... E_/github/sextractor_win/src/levmar/misc.obj
	@echo ... E_/github/sextractor_win/src/levmar/misc.i
	@echo ... E_/github/sextractor_win/src/levmar/misc.s
	@echo ... E_/github/sextractor_win/src/main.obj
	@echo ... E_/github/sextractor_win/src/main.i
	@echo ... E_/github/sextractor_win/src/main.s
	@echo ... E_/github/sextractor_win/src/makeit.obj
	@echo ... E_/github/sextractor_win/src/makeit.i
	@echo ... E_/github/sextractor_win/src/makeit.s
	@echo ... E_/github/sextractor_win/src/manobjlist.obj
	@echo ... E_/github/sextractor_win/src/manobjlist.i
	@echo ... E_/github/sextractor_win/src/manobjlist.s
	@echo ... E_/github/sextractor_win/src/misc.obj
	@echo ... E_/github/sextractor_win/src/misc.i
	@echo ... E_/github/sextractor_win/src/misc.s
	@echo ... E_/github/sextractor_win/src/neurro.obj
	@echo ... E_/github/sextractor_win/src/neurro.i
	@echo ... E_/github/sextractor_win/src/neurro.s
	@echo ... E_/github/sextractor_win/src/pattern.obj
	@echo ... E_/github/sextractor_win/src/pattern.i
	@echo ... E_/github/sextractor_win/src/pattern.s
	@echo ... E_/github/sextractor_win/src/pc.obj
	@echo ... E_/github/sextractor_win/src/pc.i
	@echo ... E_/github/sextractor_win/src/pc.s
	@echo ... E_/github/sextractor_win/src/photom.obj
	@echo ... E_/github/sextractor_win/src/photom.i
	@echo ... E_/github/sextractor_win/src/photom.s
	@echo ... E_/github/sextractor_win/src/plist.obj
	@echo ... E_/github/sextractor_win/src/plist.i
	@echo ... E_/github/sextractor_win/src/plist.s
	@echo ... E_/github/sextractor_win/src/prefs.obj
	@echo ... E_/github/sextractor_win/src/prefs.i
	@echo ... E_/github/sextractor_win/src/prefs.s
	@echo ... E_/github/sextractor_win/src/profit.obj
	@echo ... E_/github/sextractor_win/src/profit.i
	@echo ... E_/github/sextractor_win/src/profit.s
	@echo ... E_/github/sextractor_win/src/psf.obj
	@echo ... E_/github/sextractor_win/src/psf.i
	@echo ... E_/github/sextractor_win/src/psf.s
	@echo ... E_/github/sextractor_win/src/readimage.obj
	@echo ... E_/github/sextractor_win/src/readimage.i
	@echo ... E_/github/sextractor_win/src/readimage.s
	@echo ... E_/github/sextractor_win/src/refine.obj
	@echo ... E_/github/sextractor_win/src/refine.i
	@echo ... E_/github/sextractor_win/src/refine.s
	@echo ... E_/github/sextractor_win/src/retina.obj
	@echo ... E_/github/sextractor_win/src/retina.i
	@echo ... E_/github/sextractor_win/src/retina.s
	@echo ... E_/github/sextractor_win/src/scan.obj
	@echo ... E_/github/sextractor_win/src/scan.i
	@echo ... E_/github/sextractor_win/src/scan.s
	@echo ... E_/github/sextractor_win/src/som.obj
	@echo ... E_/github/sextractor_win/src/som.i
	@echo ... E_/github/sextractor_win/src/som.s
	@echo ... E_/github/sextractor_win/src/wcs/cel.obj
	@echo ... E_/github/sextractor_win/src/wcs/cel.i
	@echo ... E_/github/sextractor_win/src/wcs/cel.s
	@echo ... E_/github/sextractor_win/src/wcs/lin.obj
	@echo ... E_/github/sextractor_win/src/wcs/lin.i
	@echo ... E_/github/sextractor_win/src/wcs/lin.s
	@echo ... E_/github/sextractor_win/src/wcs/poly.obj
	@echo ... E_/github/sextractor_win/src/wcs/poly.i
	@echo ... E_/github/sextractor_win/src/wcs/poly.s
	@echo ... E_/github/sextractor_win/src/wcs/proj.obj
	@echo ... E_/github/sextractor_win/src/wcs/proj.i
	@echo ... E_/github/sextractor_win/src/wcs/proj.s
	@echo ... E_/github/sextractor_win/src/wcs/sph.obj
	@echo ... E_/github/sextractor_win/src/wcs/sph.i
	@echo ... E_/github/sextractor_win/src/wcs/sph.s
	@echo ... E_/github/sextractor_win/src/wcs/tnx.obj
	@echo ... E_/github/sextractor_win/src/wcs/tnx.i
	@echo ... E_/github/sextractor_win/src/wcs/tnx.s
	@echo ... E_/github/sextractor_win/src/wcs/wcs.obj
	@echo ... E_/github/sextractor_win/src/wcs/wcs.i
	@echo ... E_/github/sextractor_win/src/wcs/wcs.s
	@echo ... E_/github/sextractor_win/src/wcs/wcstrig.obj
	@echo ... E_/github/sextractor_win/src/wcs/wcstrig.i
	@echo ... E_/github/sextractor_win/src/wcs/wcstrig.s
	@echo ... E_/github/sextractor_win/src/weight.obj
	@echo ... E_/github/sextractor_win/src/weight.i
	@echo ... E_/github/sextractor_win/src/weight.s
	@echo ... E_/github/sextractor_win/src/winpos.obj
	@echo ... E_/github/sextractor_win/src/winpos.i
	@echo ... E_/github/sextractor_win/src/winpos.s
	@echo ... E_/github/sextractor_win/src/xml.obj
	@echo ... E_/github/sextractor_win/src/xml.i
	@echo ... E_/github/sextractor_win/src/xml.s
	@echo ... windows_stubs.obj
	@echo ... windows_stubs.i
	@echo ... windows_stubs.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

