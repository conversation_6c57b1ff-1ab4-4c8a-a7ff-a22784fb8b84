# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\cmake\bin\cmake.exe

# The command to remove a file.
RM = C:\cmake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = E:\github\sextractor_win\win

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = E:\github\sextractor_win\build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/fits.dir/all
all: CMakeFiles/wcs.dir/all
all: CMakeFiles/levmar.dir/all
all: CMakeFiles/sex.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/fits.dir/codegen
codegen: CMakeFiles/wcs.dir/codegen
codegen: CMakeFiles/levmar.dir/codegen
codegen: CMakeFiles/sex.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/fits.dir/clean
clean: CMakeFiles/wcs.dir/clean
clean: CMakeFiles/levmar.dir/clean
clean: CMakeFiles/sex.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/fits.dir

# All Build rule for target.
CMakeFiles/fits.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\github\sextractor_win\build\CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13 "Built target fits"
.PHONY : CMakeFiles/fits.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/fits.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start E:\github\sextractor_win\build\CMakeFiles 13
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/fits.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start E:\github\sextractor_win\build\CMakeFiles 0
.PHONY : CMakeFiles/fits.dir/rule

# Convenience name for target.
fits: CMakeFiles/fits.dir/rule
.PHONY : fits

# codegen rule for target.
CMakeFiles/fits.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\github\sextractor_win\build\CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13 "Finished codegen for target fits"
.PHONY : CMakeFiles/fits.dir/codegen

# clean rule for target.
CMakeFiles/fits.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\fits.dir\build.make CMakeFiles/fits.dir/clean
.PHONY : CMakeFiles/fits.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/wcs.dir

# All Build rule for target.
CMakeFiles/wcs.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\github\sextractor_win\build\CMakeFiles --progress-num=64,65,66,67,68,69,70,71,72 "Built target wcs"
.PHONY : CMakeFiles/wcs.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/wcs.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start E:\github\sextractor_win\build\CMakeFiles 9
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/wcs.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start E:\github\sextractor_win\build\CMakeFiles 0
.PHONY : CMakeFiles/wcs.dir/rule

# Convenience name for target.
wcs: CMakeFiles/wcs.dir/rule
.PHONY : wcs

# codegen rule for target.
CMakeFiles/wcs.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\github\sextractor_win\build\CMakeFiles --progress-num=64,65,66,67,68,69,70,71,72 "Finished codegen for target wcs"
.PHONY : CMakeFiles/wcs.dir/codegen

# clean rule for target.
CMakeFiles/wcs.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\wcs.dir\build.make CMakeFiles/wcs.dir/clean
.PHONY : CMakeFiles/wcs.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/levmar.dir

# All Build rule for target.
CMakeFiles/levmar.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\github\sextractor_win\build\CMakeFiles --progress-num=14,15,16,17,18,19,20,21 "Built target levmar"
.PHONY : CMakeFiles/levmar.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/levmar.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start E:\github\sextractor_win\build\CMakeFiles 8
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/levmar.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start E:\github\sextractor_win\build\CMakeFiles 0
.PHONY : CMakeFiles/levmar.dir/rule

# Convenience name for target.
levmar: CMakeFiles/levmar.dir/rule
.PHONY : levmar

# codegen rule for target.
CMakeFiles/levmar.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\github\sextractor_win\build\CMakeFiles --progress-num=14,15,16,17,18,19,20,21 "Finished codegen for target levmar"
.PHONY : CMakeFiles/levmar.dir/codegen

# clean rule for target.
CMakeFiles/levmar.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\levmar.dir\build.make CMakeFiles/levmar.dir/clean
.PHONY : CMakeFiles/levmar.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/sex.dir

# All Build rule for target.
CMakeFiles/sex.dir/all: CMakeFiles/fits.dir/all
CMakeFiles/sex.dir/all: CMakeFiles/wcs.dir/all
CMakeFiles/sex.dir/all: CMakeFiles/levmar.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\github\sextractor_win\build\CMakeFiles --progress-num=22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63 "Built target sex"
.PHONY : CMakeFiles/sex.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/sex.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start E:\github\sextractor_win\build\CMakeFiles 72
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/sex.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start E:\github\sextractor_win\build\CMakeFiles 0
.PHONY : CMakeFiles/sex.dir/rule

# Convenience name for target.
sex: CMakeFiles/sex.dir/rule
.PHONY : sex

# codegen rule for target.
CMakeFiles/sex.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=E:\github\sextractor_win\build\CMakeFiles --progress-num=22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63 "Finished codegen for target sex"
.PHONY : CMakeFiles/sex.dir/codegen

# clean rule for target.
CMakeFiles/sex.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\sex.dir\build.make CMakeFiles/sex.dir/clean
.PHONY : CMakeFiles/sex.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

