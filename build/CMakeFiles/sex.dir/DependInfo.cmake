
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "E:/github/sextractor_win/src/analyse.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/analyse.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/analyse.c.obj.d"
  "E:/github/sextractor_win/src/assoc.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/assoc.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/assoc.c.obj.d"
  "E:/github/sextractor_win/src/astrom.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/astrom.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/astrom.c.obj.d"
  "E:/github/sextractor_win/src/back.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/back.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/back.c.obj.d"
  "E:/github/sextractor_win/src/bpro.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/bpro.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/bpro.c.obj.d"
  "E:/github/sextractor_win/src/catout.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/catout.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/catout.c.obj.d"
  "E:/github/sextractor_win/src/check.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/check.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/check.c.obj.d"
  "E:/github/sextractor_win/src/clean.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/clean.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/clean.c.obj.d"
  "E:/github/sextractor_win/src/dgeo.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/dgeo.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/dgeo.c.obj.d"
  "E:/github/sextractor_win/src/extract.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/extract.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/extract.c.obj.d"
  "E:/github/sextractor_win/src/fft.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/fft.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/fft.c.obj.d"
  "E:/github/sextractor_win/src/field.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/field.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/field.c.obj.d"
  "E:/github/sextractor_win/src/filter.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/filter.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/filter.c.obj.d"
  "E:/github/sextractor_win/src/fitswcs.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/fitswcs.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/fitswcs.c.obj.d"
  "E:/github/sextractor_win/src/flag.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/flag.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/flag.c.obj.d"
  "E:/github/sextractor_win/src/graph.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/graph.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/graph.c.obj.d"
  "E:/github/sextractor_win/src/growth.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/growth.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/growth.c.obj.d"
  "E:/github/sextractor_win/src/header.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/header.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/header.c.obj.d"
  "E:/github/sextractor_win/src/image.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/image.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/image.c.obj.d"
  "E:/github/sextractor_win/src/interpolate.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/interpolate.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/interpolate.c.obj.d"
  "E:/github/sextractor_win/src/main.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/main.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/main.c.obj.d"
  "E:/github/sextractor_win/src/makeit.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/makeit.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/makeit.c.obj.d"
  "E:/github/sextractor_win/src/manobjlist.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/manobjlist.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/manobjlist.c.obj.d"
  "E:/github/sextractor_win/src/misc.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/misc.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/misc.c.obj.d"
  "E:/github/sextractor_win/src/neurro.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/neurro.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/neurro.c.obj.d"
  "E:/github/sextractor_win/src/pattern.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/pattern.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/pattern.c.obj.d"
  "E:/github/sextractor_win/src/pc.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/pc.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/pc.c.obj.d"
  "E:/github/sextractor_win/src/photom.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/photom.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/photom.c.obj.d"
  "E:/github/sextractor_win/src/plist.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/plist.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/plist.c.obj.d"
  "E:/github/sextractor_win/src/prefs.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/prefs.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/prefs.c.obj.d"
  "E:/github/sextractor_win/src/profit.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/profit.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/profit.c.obj.d"
  "E:/github/sextractor_win/src/psf.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/psf.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/psf.c.obj.d"
  "E:/github/sextractor_win/src/readimage.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/readimage.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/readimage.c.obj.d"
  "E:/github/sextractor_win/src/refine.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/refine.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/refine.c.obj.d"
  "E:/github/sextractor_win/src/retina.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/retina.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/retina.c.obj.d"
  "E:/github/sextractor_win/src/scan.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/scan.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/scan.c.obj.d"
  "E:/github/sextractor_win/src/som.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/som.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/som.c.obj.d"
  "E:/github/sextractor_win/src/weight.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/weight.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/weight.c.obj.d"
  "E:/github/sextractor_win/src/winpos.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/winpos.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/winpos.c.obj.d"
  "E:/github/sextractor_win/src/xml.c" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/xml.c.obj" "gcc" "CMakeFiles/sex.dir/E_/github/sextractor_win/src/xml.c.obj.d"
  "E:/github/sextractor_win/win/windows_stubs.c" "CMakeFiles/sex.dir/windows_stubs.c.obj" "gcc" "CMakeFiles/sex.dir/windows_stubs.c.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
