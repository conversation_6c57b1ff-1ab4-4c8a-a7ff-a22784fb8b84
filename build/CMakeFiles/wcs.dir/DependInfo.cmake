
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "E:/github/sextractor_win/src/wcs/cel.c" "CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/cel.c.obj" "gcc" "CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/cel.c.obj.d"
  "E:/github/sextractor_win/src/wcs/lin.c" "CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/lin.c.obj" "gcc" "CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/lin.c.obj.d"
  "E:/github/sextractor_win/src/wcs/poly.c" "CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/poly.c.obj" "gcc" "CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/poly.c.obj.d"
  "E:/github/sextractor_win/src/wcs/proj.c" "CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/proj.c.obj" "gcc" "CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/proj.c.obj.d"
  "E:/github/sextractor_win/src/wcs/sph.c" "CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/sph.c.obj" "gcc" "CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/sph.c.obj.d"
  "E:/github/sextractor_win/src/wcs/tnx.c" "CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/tnx.c.obj" "gcc" "CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/tnx.c.obj.d"
  "E:/github/sextractor_win/src/wcs/wcs.c" "CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcs.c.obj" "gcc" "CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcs.c.obj.d"
  "E:/github/sextractor_win/src/wcs/wcstrig.c" "CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcstrig.c.obj" "gcc" "CMakeFiles/wcs.dir/E_/github/sextractor_win/src/wcs/wcstrig.c.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
