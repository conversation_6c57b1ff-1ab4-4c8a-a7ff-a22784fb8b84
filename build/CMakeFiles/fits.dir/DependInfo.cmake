
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "E:/github/sextractor_win/src/fits/fitsbody.c" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsbody.c.obj" "gcc" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsbody.c.obj.d"
  "E:/github/sextractor_win/src/fits/fitscat.c" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscat.c.obj" "gcc" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscat.c.obj.d"
  "E:/github/sextractor_win/src/fits/fitscheck.c" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscheck.c.obj" "gcc" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscheck.c.obj.d"
  "E:/github/sextractor_win/src/fits/fitscleanup.c" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscleanup.c.obj" "gcc" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitscleanup.c.obj.d"
  "E:/github/sextractor_win/src/fits/fitsconv.c" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsconv.c.obj" "gcc" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsconv.c.obj.d"
  "E:/github/sextractor_win/src/fits/fitshead.c" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitshead.c.obj" "gcc" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitshead.c.obj.d"
  "E:/github/sextractor_win/src/fits/fitskey.c" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitskey.c.obj" "gcc" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitskey.c.obj.d"
  "E:/github/sextractor_win/src/fits/fitsmisc.c" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsmisc.c.obj" "gcc" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsmisc.c.obj.d"
  "E:/github/sextractor_win/src/fits/fitsread.c" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsread.c.obj" "gcc" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsread.c.obj.d"
  "E:/github/sextractor_win/src/fits/fitstab.c" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitstab.c.obj" "gcc" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitstab.c.obj.d"
  "E:/github/sextractor_win/src/fits/fitsutil.c" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsutil.c.obj" "gcc" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitsutil.c.obj.d"
  "E:/github/sextractor_win/src/fits/fitswrite.c" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitswrite.c.obj" "gcc" "CMakeFiles/fits.dir/E_/github/sextractor_win/src/fits/fitswrite.c.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
