#
#				sextractor.spec.in
#
# Process this file with autoconf to generate an RPM .spec packaging script.
#
#%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
#
#	This file part of:	SExtractor
#
#	Copyright:		(C) 2002-2022 IAP/CNRS/SorbonneU
#
#	License:		GNU General Public License
#
#	SExtractor is free software: you can redistribute it and/or modify
#	it under the terms of the GNU General Public License as published by
#	the Free Software Foundation, either version 3 of the License, or
# 	(at your option) any later version.
#	SExtractor is distributed in the hope that it will be useful,
#	but WITHOUT ANY WARRANTY; without even the implied warranty of
#	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#	GNU General Public License for more details.
#	You should have received a copy of the GNU General Public License
#	along with SExtractor. If not, see <http://www.gnu.org/licenses/>.
#
#	Last modified:		15/09/2022
#
#%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

%define name @PACKAGE_TARNAME@
%define version @PACKAGE_VERSION@
%define release @PACKAGE_RELEASE@%{?dist}
%define debug_package %{nil}
%undefine _missing_build_ids_terminate_build

Summary: Extract catalogs of sources from astronomical images
Name: %{name}
Version: %{version}
Release: %{release}
Source0: http://astromatic.net/download/%{name}/%{name}-%{version}.tar.gz
URL: http://astromatic.net/software/%{name}/
License: GPL v3+
Group: Sciences/Astronomy
BuildRoot: %{_tmppath}/%{name}-buildroot
BuildRequires: pkgconfig
BuildRequires: fftw-devel >= 3.1
BuildRequires: atlas-devel >= 3.6.0
BuildRequires: cfitsio-devel >= 3.30

%description
Extract catalogs of sources from astronomical images

%prep
%setup -q

%build
if test "$USE_BEST"; then
%configure --enable-mkl --enable-auto-flags --enable-best-link --with-release=@PACKAGE_RELEASE@
elif test "$USE_ICC"; then
%configure --enable-icc --with-release=@PACKAGE_RELEASE@
else
%configure --with-release=@PACKAGE_RELEASE@
fi
make %{?_smp_mflags}

%install
rm -rf $RPM_BUILD_ROOT
make install DESTDIR=$RPM_BUILD_ROOT

%clean
rm -rf $RPM_BUILD_ROOT

%files
%defattr(-,root,root)
%doc config AUTHORS BUGS ChangeLog COPYRIGHT HISTORY INSTALL LICENSE README.md THANKS
%{_bindir}/sex
%{_bindir}/ldactoasc
%{_mandir}/man1/sex.1*
%{_mandir}/manx/sex.x*
%{_datadir}/@PACKAGE_TARNAME@

%changelog
* @DATE2@ @PACKAGER@ <@PACKAGE_BUGREPORT@>
- Automatic RPM rebuild
* Tue May 13 2003 Emmanuel Bertin <<EMAIL>>
- RPM build for V2.3
* Fri Apr 04 2003 Emmanuel Bertin <<EMAIL>>
- RPM build for V2.3b4
* Wed Mar 05 2003 Emmanuel Bertin <<EMAIL>>
- RPM build for V2.3b3
* Fri Feb 07 2003 Emmanuel Bertin <<EMAIL>>
- Second RPM build
* Fri Jan 24 2003 Emmanuel Bertin <<EMAIL>>
- Second RPM build
* Sun Dec 15 2002 Emmanuel Bertin <<EMAIL>>
- First RPM build

# end of file
