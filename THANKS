<PERSON><PERSON><PERSON><PERSON>uts
	Suggestions and extensive testing
<PERSON>
	For her preparatory work on galaxy model-fitting
<PERSON>
	Suggestions and bug fixes
<PERSON> about how to optimize a C code
Anthony <PERSON>
	Testing of model-fitting
<PERSON>
	Helping with error estimates
Mark <PERSON>ab<PERSON>
	SExtractor uses an early version of his great WCS library
Philippe <PERSON>
	Debugging and improving PSF-fitting
<PERSON><PERSON>
	for solving the $datarootdir issue and providing the URBI_RESOLVE_DIR()
	function.
<PERSON><PERSON><PERSON>
	Finding well-hidden bugs.
<PERSON>
	Suggestions and testing
<PERSON><PERSON><PERSON>
	Checking shape measurements
Delphine Hardin
	Suggestions and bug fixes
<PERSON><PERSON>
	For making his cookbook available
Jean<PERSON><PERSON>, <PERSON>mail
	Suggestions and spreading the software back in the early days! 
<PERSON>ert
	Helping SExtractor users
<PERSON>
	For his VL_PROG_CC_WARNINGS (modified to ACX_PROG_CC_OPTIM)
Valérie de Lapparent
	Suggestions and testing of model-fitting
<PERSON><PERSON>is
	For his implementation of the Levenberg-Marquardt algorithm
Chiara Marmo
	For providing the VOTable to HTML XSLT filter and testing.
<PERSON>hr
	Suggestions and testing
<PERSON>
	Helping with autoconfig and packaging
Nacho Sevilla
	Testing star/galaxy classification
Víctor <PERSON>
	Suggesting and documenting DETECT_MAXAREA
Patrick Tisserand
	Testing PSF-fitting
Gérard Tissier
	Preliminary implementation of the VOTable output

Institut d'Astrophysique de Paris
DeNIS project
European Southern Observatory
Sterrewacht Leiden
TERAPIX project
French Ministry of Research Grant 04-5500 (“ACI masse de données”)
University of Illinois
Dark Energy Survey data management project
	Financial and technical support

Too many people to list
	Help in discovering bugs or bringing suggestions

