Installing the software
***********************

Hardware requirements
=====================

SExtractor runs in (ANSI) text-mode from a shell. A window system is
not necessary for basic operation.

When it comes to memory usage, the amount required by SExtractor depends
mostly on the size of the input images and especially on the presence of
the so-called "check images". But in most cases it is less than one
gigabyte.


Obtaining SExtractor
====================

For Linux users, the simplest way to have SExtractor up and running is
to install the standard binary package that comes with your Linux
distribution. Run, e.g., "apt-get sextractor" (on Debian) or
"dnf install sextractor" (Fedora) and SExtractor, as well as all its
dependencies, will automatically be installed. If you decided to install
the package this way you're all set!
However if SExtractor is not available in your distribution, or to
obtain the most recent version, the SExtractor source package can be
downloaded from the official GitHub repository. One may choose one of
the stable releases, or for the fearless, a copy of the current master
development branch.


Software requirements
=====================

SExtractor has been developed on GNU/Linux machines and should compile
on any POSIX-compliant system (this includes Apple OS X and Cygwin
on Microsoft Windows, at the price of some difficulties with the
configuration), provided that the development packages of the
following libraries have been installed:

* ATLAS V3.6 and above [2],

* FFTw V3.0 and above [3],

On Fedora/Redhat distributions for instance, the development packages
above are available as "atlas-devel" and "fftw-devel".
ATLAS and FFTw are not necessary if SExtractor is linked with
Intel's MKL (Math Kernel Library) library.


Installation
============

To install from the GitHub source package, you must first uncompress
the archive:

   $ unzip sextractor-<version>.zip

A new directory called "sextractor-<version>" should now appear at the
current location on your disk. Enter the directory and generate the
files required by the autotools, which the package relies on:

   $ cd sextractor-<version>
   $ sh autogen.sh

A "configure" script is created. This script has many options, which
may be listed with the "--help" option:

   $ ./configure --help

No options are required for compiling with the default GNU C compiler
("gcc") if all the required libraries are installed at their default
locations:

   $ ./configure

Compared to "gcc" and the librairies above, the combination of the
Intel compiler ("icc" or "icx") and the MKL (Math Kernel Library)
libraries can give the SExtractor executable a significant boost in
performance, thanks to better vectorized code, and reduced memory
usage. If "icc" (or "icx") and the MKL (Math Kernel Library) are
installed on your system [4], you can take advantage of those using
instead

   $ ./configure --enable-mkl

Additionally, if the SExtractor binary is to be run on a different
machine that does not have "icc" (or "icx") and the MKL installed
(e.g., a cluster computing node), you must configure a partially
statically linked executable using

   $ ./configure --enable-mkl --enable-auto-flags --enable-best-link

In all cases, SExtractor can now be compiled with

   $ make -j

An "src/sex" executable is created. For system-wide installation,
run the usual

   $ sudo make install

You may now check that the software is properly installed by simply
typing in your shell:

   $ sex

(note that some shells require the "rehash" command to be run before
making a freshly installed executable accessible in the execution
path).

[1] Mac OS X ".dmg" packages should be available soon.

[2] Use the "--with-atlas" and/or "--with-atlas-incdir" options of
    the SExtractor "configure" script to specify the ATLAS library and
    include paths if ATLAS files are installed at unusual locations.

[3] Make sure that FFTw has been compiled with "configure"
    options "--enable-threads --enable-float".

[4] The Linux versions of the Intel compiler and MKL (Math
    Kernel Library) are now available for free on Intel's website.

