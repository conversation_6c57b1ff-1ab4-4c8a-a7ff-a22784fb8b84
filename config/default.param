NUMBER                    # Running object number                                    
#EXT_NUMBER               FITS extension number                                    
#FLUX_ISO                 Isophotal flux                                            [count]
#FLUXERR_ISO              RMS error for isophotal flux                              [count]
#MAG_ISO                  Isophotal magnitude                                       [mag]
#MAGERR_ISO               RMS error for isophotal magnitude                         [mag]
#FLUX_ISOCOR              Corrected isophotal flux                                  [count]
#FLUXERR_ISOCOR           RMS error for corrected isophotal flux                    [count]
#MAG_ISOCOR               Corrected isophotal magnitude                             [mag]
#MAGERR_ISOCOR            RMS error for corrected isophotal magnitude               [mag]
#FLUX_APER                Flux vector within fixed circular aperture(s)             [count]
#FLUXERR_APER             RMS error vector for aperture flux(es)                    [count]
#MAG_APER                 Fixed aperture magnitude vector                           [mag]
#MAGERR_APER              RMS error vector for fixed aperture mag.                  [mag]
#FLUX_AUTO                Flux within a Kron-like elliptical aperture               [count]
#FLUXERR_AUTO             RMS error for AUTO flux                                   [count]
#MAG_AUTO                 Kron-like elliptical aperture magnitude                   [mag]
#MAGERR_AUTO              RMS error for AUTO magnitude                              [mag]
#FLUX_PETRO               Flux within a Petrosian-like elliptical aperture          [count]
#FLUXERR_PETRO            RMS error for PETROsian flux                              [count]
#MAG_PETRO                Petrosian-like elliptical aperture magnitude              [mag]
#MAGERR_PETRO             RMS error for PETROsian magnitude                         [mag]
#FLUX_BEST                Best of FLUX_AUTO and FLUX_ISOCOR                         [count]
#FLUXERR_BEST             RMS error for BEST flux                                   [count]
#MAG_BEST                 Best of MAG_AUTO and MAG_ISOCOR                           [mag]
#MAGERR_BEST              RMS error for MAG_BEST                                    [mag]
#FLUX_WIN                 Gaussian-weighted flux                                    [count]
#FLUXERR_WIN              RMS error for WIN flux                                    [count]
#MAG_WIN                  Gaussian-weighted magnitude                               [mag]
#MAGERR_WIN               RMS error for MAG_WIN                                     [mag]
#FLUX_SOMFIT              Flux derived from SOM fit                                 [count]
#FLUXERR_SOMFIT           RMS error for SOMFIT flux                                 [count]
#MAG_SOMFIT               Magnitude derived from SOM fit                            [mag]
#MAGERR_SOMFIT            Magnitude error derived from SOM fit                      [mag]
#ERROR_SOMFIT             Reduced Chi-square error of the SOM fit                  
#VECTOR_SOMFIT            Position vector of the winning SOM node                  
#KRON_RADIUS              Kron apertures in units of A or B                        
#PETRO_RADIUS             Petrosian apertures in units of A or B                   
#BACKGROUND               Background at centroid position                           [count]
#THRESHOLD                Detection threshold above background                      [count]
#FLUX_MAX                 Peak flux above background                                [count]
#ISOAREA_IMAGE            Isophotal area above Analysis threshold                   [pixel**2]
#ISOAREAF_IMAGE           Isophotal area (filtered) above Detection threshold       [pixel**2]
#XMIN_IMAGE               Minimum x-coordinate among detected pixels                [pixel]
#YMIN_IMAGE               Minimum y-coordinate among detected pixels                [pixel]
#XMAX_IMAGE               Maximum x-coordinate among detected pixels                [pixel]
#YMAX_IMAGE               Maximum y-coordinate among detected pixels                [pixel]
#XPEAK_IMAGE              x-coordinate of the brightest pixel                       [pixel]
#YPEAK_IMAGE              y-coordinate of the brightest pixel                       [pixel]
#XPEAK_FOCAL              Focal-plane x coordinate of the brightest pixel          
#YPEAK_FOCAL              Focal-plane y coordinate of the brightest pixel          
#XPEAK_WORLD              World-x coordinate of the brightest pixel                 [deg]
#YPEAK_WORLD              World-y coordinate of the brightest pixel                 [deg]
#ALPHAPEAK_SKY            Right ascension of brightest pix (native)                 [deg]
#DELTAPEAK_SKY            Declination of brightest pix (native)                     [deg]
#ALPHAPEAK_J2000          Right ascension of brightest pix (J2000)                  [deg]
#DELTAPEAK_J2000          Declination of brightest pix (J2000)                      [deg]
#ALPHAPEAK_B1950          Right ascension of brightest pix (B1950)                  [deg]
#DELTAPEAK_B1950          Declination of brightest pix (B1950)                      [deg]
X_IMAGE                   # Object position along x                                 [pixel]
Y_IMAGE                   # Object position along y                                 [pixel]
#X_IMAGE_DBL              Object position along x (double precision)                [pixel]
#Y_IMAGE_DBL              Object position along y (double precision)                [pixel]
#X_FOCAL                  Barycenter position along focal-plane x axis             
#Y_FOCAL                  Barycenter position along focal-plane y axis             
#X_WORLD                  Barycenter position along world x axis                    [deg]
#Y_WORLD                  Barycenter position along world y axis                    [deg]
#X_MAMA                   Barycenter position along MAMA x axis                     [m**(-6)]
#Y_MAMA                   Barycenter position along MAMA y axis                     [m**(-6)]
#ALPHA_SKY                Right ascension of barycenter (native)                    [deg]
#DELTA_SKY                Declination of barycenter (native)                        [deg]
#ALPHA_J2000              Right ascension of barycenter (J2000)                     [deg]
#DELTA_J2000              Declination of barycenter (J2000)                         [deg]
#ALPHA_B1950              Right ascension of barycenter (B1950)                     [deg]
#DELTA_B1950              Declination of barycenter (B1950)                         [deg]
#X2_IMAGE                 Variance along x                                          [pixel**2]
#Y2_IMAGE                 Variance along y                                          [pixel**2]
#XY_IMAGE                 Covariance between x and y                                [pixel**2]
#X2_WORLD                 Variance along X-WORLD (alpha)                            [deg**2]
#Y2_WORLD                 Variance along Y-WORLD (delta)                            [deg**2]
#XY_WORLD                 Covariance between X-WORLD and Y-WORLD                    [deg**2]
#CXX_IMAGE                Cxx object ellipse parameter                              [pixel**(-2)]
#CYY_IMAGE                Cyy object ellipse parameter                              [pixel**(-2)]
#CXY_IMAGE                Cxy object ellipse parameter                              [pixel**(-2)]
#CXX_WORLD                Cxx object ellipse parameter (WORLD units)                [deg**(-2)]
#CYY_WORLD                Cyy object ellipse parameter (WORLD units)                [deg**(-2)]
#CXY_WORLD                Cxy object ellipse parameter (WORLD units)                [deg**(-2)]
#A_IMAGE                  Profile RMS along major axis                              [pixel]
#B_IMAGE                  Profile RMS along minor axis                              [pixel]
#THETA_IMAGE              Position angle (CCW/x)                                    [deg]
#A_WORLD                  Profile RMS along major axis (world units)                [deg]
#B_WORLD                  Profile RMS along minor axis (world units)                [deg]
#THETA_WORLD              Position angle (CCW/world-x)                              [deg]
#THETA_SKY                Position angle (east of north) (native)                   [deg]
#THETA_J2000              Position angle (east of north) (J2000)                    [deg]
#THETA_B1950              Position angle (east of north) (B1950)                    [deg]
#ERRX2_IMAGE              Variance of position along x                              [pixel**2]
#ERRY2_IMAGE              Variance of position along y                              [pixel**2]
#ERRXY_IMAGE              Covariance of position between x and y                    [pixel**2]
#ERRX2_WORLD              Variance of position along X-WORLD (alpha)                [deg**2]
#ERRY2_WORLD              Variance of position along Y-WORLD (delta)                [deg**2]
#ERRXY_WORLD              Covariance of position X-WORLD/Y-WORLD                    [deg**2]
#ERRCXX_IMAGE             Cxx error ellipse parameter                               [pixel**(-2)]
#ERRCYY_IMAGE             Cyy error ellipse parameter                               [pixel**(-2)]
#ERRCXY_IMAGE             Cxy error ellipse parameter                               [pixel**(-2)]
#ERRCXX_WORLD             Cxx error ellipse parameter (WORLD units)                 [deg**(-2)]
#ERRCYY_WORLD             Cyy error ellipse parameter (WORLD units)                 [deg**(-2)]
#ERRCXY_WORLD             Cxy error ellipse parameter (WORLD units)                 [deg**(-2)]
#ERRA_IMAGE               RMS position error along major axis                       [pixel]
#ERRB_IMAGE               RMS position error along minor axis                       [pixel]
#ERRTHETA_IMAGE           Error ellipse position angle (CCW/x)                      [deg]
#ERRA_WORLD               World RMS position error along major axis                 [deg]
#ERRB_WORLD               World RMS position error along minor axis                 [deg]
#ERRTHETA_WORLD           Error ellipse pos. angle (CCW/world-x)                    [deg]
#ERRTHETA_SKY             Native error ellipse pos. angle (east of north)           [deg]
#ERRTHETA_J2000           J2000 error ellipse pos. angle (east of north)            [deg]
#ERRTHETA_B1950           B1950 error ellipse pos. angle (east of north)            [deg]
#XWIN_IMAGE               Windowed position estimate along x                        [pixel]
#YWIN_IMAGE               Windowed position estimate along y                        [pixel]
#XWIN_FOCAL               Windowed position along focal-plane x axis               
#YWIN_FOCAL               Windowed position along focal-plane y axis               
#XWIN_WORLD               Windowed position along world x axis                      [deg]
#YWIN_WORLD               Windowed position along world y axis                      [deg]
#ALPHAWIN_SKY             Windowed right ascension  (native)                        [deg]
#DELTAWIN_SKY             Windowed declination (native)                             [deg]
#ALPHAWIN_J2000           Windowed right ascension (J2000)                          [deg]
#DELTAWIN_J2000           windowed declination (J2000)                              [deg]
#ALPHAWIN_B1950           Windowed right ascension (B1950)                          [deg]
#DELTAWIN_B1950           Windowed declination (B1950)                              [deg]
#X2WIN_IMAGE              Windowed variance along x                                 [pixel**2]
#Y2WIN_IMAGE              Windowed variance along y                                 [pixel**2]
#XYWIN_IMAGE              Windowed covariance between x and y                       [pixel**2]
#X2WIN_WORLD              Windowed variance along X-WORLD (alpha)                   [deg**2]
#Y2WIN_WORLD              Windowed variance along Y-WORLD (delta)                   [deg**2]
#XYWIN_WORLD              Windowed covariance between X-WORLD and Y-WORLD           [deg**2]
#CXXWIN_IMAGE             Windowed Cxx object ellipse parameter                     [pixel**(-2)]
#CYYWIN_IMAGE             Windowed Cyy object ellipse parameter                     [pixel**(-2)]
#CXYWIN_IMAGE             Windowed Cxy object ellipse parameter                     [pixel**(-2)]
#CXXWIN_WORLD             Windowed Cxx object ellipse parameter (WORLD units)       [deg**(-2)]
#CYYWIN_WORLD             Windowed Cyy object ellipse parameter (WORLD units)       [deg**(-2)]
#CXYWIN_WORLD             Windowed Cxy object ellipse parameter (WORLD units)       [deg**(-2)]
#AWIN_IMAGE               Windowed profile RMS along major axis                     [pixel]
#BWIN_IMAGE               Windowed profile RMS along minor axis                     [pixel]
#THETAWIN_IMAGE           Windowed position angle (CCW/x)                           [deg]
#AWIN_WORLD               Windowed profile RMS along major axis (world units)       [deg]
#BWIN_WORLD               Windowed profile RMS along minor axis (world units)       [deg]
#THETAWIN_WORLD           Windowed position angle (CCW/world-x)                     [deg]
#THETAWIN_SKY             Windowed position angle (east of north) (native)          [deg]
#THETAWIN_J2000           Windowed position angle (east of north) (J2000)           [deg]
#THETAWIN_B1950           Windowed position angle (east of north) (B1950)           [deg]
#ERRX2WIN_IMAGE           Variance of windowed pos along x                          [pixel**2]
#ERRY2WIN_IMAGE           Variance of windowed pos along y                          [pixel**2]
#ERRXYWIN_IMAGE           Covariance of windowed pos between x and y                [pixel**2]
#ERRX2WIN_WORLD           Variance of windowed pos along X-WORLD (alpha)            [deg**2]
#ERRY2WIN_WORLD           Variance of windowed pos along Y-WORLD (delta)            [deg**2]
#ERRXYWIN_WORLD           Covariance of windowed pos X-WORLD/Y-WORLD                [deg**2]
#ERRCXXWIN_IMAGE          Cxx windowed error ellipse parameter                      [pixel**(-2)]
#ERRCYYWIN_IMAGE          Cyy windowed error ellipse parameter                      [pixel**(-2)]
#ERRCXYWIN_IMAGE          Cxy windowed error ellipse parameter                      [pixel**(-2)]
#ERRCXXWIN_WORLD          Cxx windowed error ellipse parameter (WORLD units)        [deg**(-2)]
#ERRCYYWIN_WORLD          Cyy windowed error ellipse parameter (WORLD units)        [deg**(-2)]
#ERRCXYWIN_WORLD          Cxy windowed error ellipse parameter (WORLD units)        [deg**(-2)]
#ERRAWIN_IMAGE            RMS windowed pos error along major axis                   [pixel]
#ERRBWIN_IMAGE            RMS windowed pos error along minor axis                   [pixel]
#ERRTHETAWIN_IMAGE        Windowed error ellipse pos angle (CCW/x)                  [deg]
#ERRAWIN_WORLD            World RMS windowed pos error along major axis             [deg]
#ERRBWIN_WORLD            World RMS windowed pos error along minor axis             [deg]
#ERRTHETAWIN_WORLD        Windowed error ellipse pos. angle (CCW/world-x)           [deg]
#ERRTHETAWIN_SKY          Native windowed error ellipse pos. angle (east of north)  [deg]
#ERRTHETAWIN_J2000        J2000 windowed error ellipse pos. angle (east of north)   [deg]
#ERRTHETAWIN_B1950        B1950 windowed error ellipse pos. angle (east of north)   [deg]
#NITER_WIN                Number of iterations for WIN centering                   
#MU_THRESHOLD             Detection threshold above background                      [mag * arcsec**(-2)]
#MU_MAX                   Peak surface brightness above background                  [mag * arcsec**(-2)]
#ISOAREA_WORLD            Isophotal area above Analysis threshold                   [deg**2]
#ISOAREAF_WORLD           Isophotal area (filtered) above Detection threshold       [deg**2]
#ISO0                     Isophotal area at level 0                                 [pixel**2]
#ISO1                     Isophotal area at level 1                                 [pixel**2]
#ISO2                     Isophotal area at level 2                                 [pixel**2]
#ISO3                     Isophotal area at level 3                                 [pixel**2]
#ISO4                     Isophotal area at level 4                                 [pixel**2]
#ISO5                     Isophotal area at level 5                                 [pixel**2]
#ISO6                     Isophotal area at level 6                                 [pixel**2]
#ISO7                     Isophotal area at level 7                                 [pixel**2]
FLAGS                     # Extraction flags                                         
#FLAGS_WEIGHT             Weighted extraction flags                                
#FLAGS_WIN                Flags for WINdowed parameters                            
#IMAFLAGS_ISO             FLAG-image flags OR'ed over the iso. profile             
#NIMAFLAGS_ISO            Number of flagged pixels entering IMAFLAGS_ISO           
#NLOWWEIGHT_ISO           Nb of pixels with low weight over the iso. profile       
#NLOWDWEIGHT_ISO          Nb of pixels with low det. weight over the iso. profile  
#FWHM_IMAGE               FWHM assuming a gaussian core                             [pixel]
#FWHM_WORLD               FWHM assuming a gaussian core                             [deg]
#ELONGATION               A_IMAGE/B_IMAGE                                          
#ELLIPTICITY              1 - B_IMAGE/A_IMAGE                                      
#POLAR_IMAGE              (A_IMAGE^2 - B_IMAGE^2)/(A_IMAGE^2 + B_IMAGE^2)          
#POLAR_WORLD              (A_WORLD^2 - B_WORLD^2)/(A_WORLD^2 + B_WORLD^2)          
#POLARWIN_IMAGE           (AWIN^2 - BWIN^2)/(AWIN^2 + BWIN^2)                      
#POLARWIN_WORLD           (AWIN^2 - BWIN^2)/(AWIN^2 + BWIN^2)                      
#CLASS_STAR               S/G classifier output                                    
#VIGNET                   Pixel data around detection                               [count]
#VIGNET_SHIFT             Pixel data around detection, corrected for shift          [count]
#VECTOR_ASSOC             ASSOCiated parameter vector                              
#NUMBER_ASSOC             Number of ASSOCiated IDs                                 
#THRESHOLDMAX             Maximum threshold possible for detection                  [count]
#FLUX_GROWTH              Cumulated growth-curve                                    [count]
#FLUX_GROWTHSTEP          Step for growth-curves                                    [pixel]
#MAG_GROWTH               Cumulated magnitude growth-curve                          [mag]
#MAG_GROWTHSTEP           Step for growth-curves                                    [pixel]
#FLUX_RADIUS              Fraction-of-light radii                                   [pixel]
#XPSF_IMAGE               X coordinate from PSF-fitting                             [pixel]
#YPSF_IMAGE               Y coordinate from PSF-fitting                             [pixel]
#XPSF_WORLD               PSF position along world x axis                           [deg]
#YPSF_WORLD               PSF position along world y axis                           [deg]
#ALPHAPSF_SKY             Right ascension of the fitted PSF (native)                [deg]
#DELTAPSF_SKY             Declination of the fitted PSF (native)                    [deg]
#ALPHAPSF_J2000           Right ascension of the fitted PSF (J2000)                 [deg]
#DELTAPSF_J2000           Declination of the fitted PSF (J2000)                     [deg]
#ALPHAPSF_B1950           Right ascension of the fitted PSF (B1950)                 [deg]
#DELTAPSF_B1950           Declination of the fitted PSF (B1950)                     [deg]
#FLUX_PSF                 Flux from PSF-fitting                                     [count]
#FLUXERR_PSF              RMS flux error for PSF-fitting                            [count]
#MAG_PSF                  Magnitude from PSF-fitting                                [mag]
#MAGERR_PSF               RMS magnitude error from PSF-fitting                      [mag]
#NITER_PSF                Number of iterations for PSF-fitting                     
#CHI2_PSF                 Reduced chi2 from PSF-fitting                            
#ERRX2PSF_IMAGE           Variance of PSF position along x                          [pixel**2]
#ERRY2PSF_IMAGE           Variance of PSF position along y                          [pixel**2]
#ERRXYPSF_IMAGE           Covariance of PSF position between x and y                [pixel**2]
#ERRX2PSF_WORLD           Variance of PSF position along X-WORLD (alpha)            [deg**2]
#ERRY2PSF_WORLD           Variance of PSF position along Y-WORLD (delta)            [deg**2]
#ERRXYPSF_WORLD           Covariance of PSF position X-WORLD/Y-WORLD                [deg**2]
#ERRCXXPSF_IMAGE          Cxx PSF error ellipse parameter                           [pixel**(-2)]
#ERRCYYPSF_IMAGE          Cyy PSF error ellipse parameter                           [pixel**(-2)]
#ERRCXYPSF_IMAGE          Cxy PSF error ellipse parameter                           [pixel**(-2)]
#ERRCXXPSF_WORLD          Cxx PSF error ellipse parameter (WORLD units)             [deg**(-2)]
#ERRCYYPSF_WORLD          Cyy PSF error ellipse parameter (WORLD units)             [deg**(-2)]
#ERRCXYPSF_WORLD          Cxy PSF error ellipse parameter (WORLD units)             [deg**(-2)]
#ERRAPSF_IMAGE            PSF RMS position error along major axis                   [pixel]
#ERRBPSF_IMAGE            PSF RMS position error along minor axis                   [pixel]
#ERRTHETAPSF_IMAGE        PSF error ellipse position angle (CCW/x)                  [deg]
#ERRAPSF_WORLD            World PSF RMS position error along major axis             [pixel]
#ERRBPSF_WORLD            World PSF RMS position error along minor axis             [pixel]
#ERRTHETAPSF_WORLD        PSF error ellipse pos. angle (CCW/world-x)                [deg]
#ERRTHETAPSF_SKY          Native PSF error ellipse pos. angle (east of north)       [deg]
#ERRTHETAPSF_J2000        J2000 PSF error ellipse pos. angle (east of north)        [deg]
#ERRTHETAPSF_B1950        B1950 PSF error ellipse pos. angle (east of north)        [deg]
#DURATION_ANALYSIS        Duration of analysis for this source                      [s]
#VECTOR_MODEL             Model-fitting coefficients                               
#VECTOR_MODELERR          Model-fitting coefficient uncertainties                  
#MATRIX_MODELERR          Model-fitting covariance matrix                          
#CHI2_MODEL               Reduced Chi2 of the fit                                  
#FLAGS_MODEL              Model-fitting flags                                      
#NITER_MODEL              Number of iterations for model-fitting                   
#FLUX_MODEL               Flux from model-fitting                                   [count]
#FLUXERR_MODEL            RMS error on model-fitting flux                           [count]
#MAG_MODEL                Magnitude from model-fitting                              [mag]
#MAGERR_MODEL             RMS error on model-fitting magnitude                      [mag]
#FLUX_MAX_MODEL           Peak model flux above background                          [count]
#FLUX_EFF_MODEL           Effective model flux above background                     [count]
#FLUX_MEAN_MODEL          Mean effective model flux above background                [count]
#MU_MAX_MODEL             Peak model surface brightness above background            [mag * arcsec**(-2)]
#MU_EFF_MODEL             Effective model surface brightness above background       [mag * arcsec**(-2)]
#MU_MEAN_MODEL            Mean effective model surface brightness above background  [mag * arcsec**(-2)]
#XMODEL_IMAGE             X coordinate from model-fitting                           [pixel]
#YMODEL_IMAGE             Y coordinate from model-fitting                           [pixel]
#XFOCAL_WORLD             Fitted position along focal-plane x axis                 
#YFOCAL_WORLD             Fitted position along focal-plane y axis                 
#XMODEL_WORLD             Fitted position along world x axis                        [deg]
#YMODEL_WORLD             Fitted position along world y axis                        [deg]
#ALPHAMODEL_SKY           Fitted position along right ascension  (native)           [deg]
#DELTAMODEL_SKY           Fitted position along declination (native)                [deg]
#ALPHAMODEL_J2000         Fitted position along right ascension (J2000)             [deg]
#DELTAMODEL_J2000         Fitted position along declination (J2000)                 [deg]
#ALPHAMODEL_B1950         Fitted position along right ascension (B1950)             [deg]
#DELTAMODEL_B1950         Fitted position along declination (B1950)                 [deg]
#ERRX2MODEL_IMAGE         Variance of fitted position along x                       [pixel**2]
#ERRY2MODEL_IMAGE         Variance of fitted position along y                       [pixel**2]
#ERRXYMODEL_IMAGE         Covariance of fitted position between x and y             [pixel**2]
#ERRX2MODEL_WORLD         Variance of fitted position along X-WORLD (alpha)         [deg**2]
#ERRY2MODEL_WORLD         Variance of fitted position along Y-WORLD (delta)         [deg**2]
#ERRXYMODEL_WORLD         Covariance of fitted position X-WORLD/Y-WORLD             [deg**2]
#ERRCXXMODEL_IMAGE        Cxx error ellipse parameter of fitted position            [pixel**(-2)]
#ERRCYYMODEL_IMAGE        Cyy error ellipse parameter of fitted position            [pixel**(-2)]
#ERRCXYMODEL_IMAGE        Cxy error ellipse parameter of fitted position            [pixel**(-2)]
#ERRCXXMODEL_WORLD        Cxx fitted error ellipse parameter (WORLD units)          [deg**(-2)]
#ERRCYYMODEL_WORLD        Cyy fitted error ellipse parameter (WORLD units)          [deg**(-2)]
#ERRCXYMODEL_WORLD        Cxy fitted error ellipse parameter (WORLD units)          [deg**(-2)]
#ERRAMODEL_IMAGE          RMS error of fitted position along major axis             [pixel]
#ERRBMODEL_IMAGE          RMS error of fitted position along minor axis             [pixel]
#ERRTHETAMODEL_IMAGE      Error ellipse pos.angle of fitted position (CCW/x)        [deg]
#ERRAMODEL_WORLD          World RMS error of fitted position along major axis       [deg]
#ERRBMODEL_WORLD          World RMS error of fitted position along minor axis       [deg]
#ERRTHETAMODEL_WORLD      Error ellipse pos.angle of fitted position (CCW/world-x)  [deg]
#ERRTHETAMODEL_SKY        Native fitted error ellipse pos. angle (east of north)    [deg]
#ERRTHETAMODEL_J2000      J2000 fitted error ellipse pos. angle (east of north)     [deg]
#ERRTHETAMODEL_B1950      B1950 fitted error ellipse pos. angle (east of north)     [deg]
#X2MODEL_IMAGE            Variance along x from model-fitting                       [pixel**2]
#Y2MODEL_IMAGE            Variance along y from model-fitting                       [pixel**2]
#XYMODEL_IMAGE            Covariance between x and y from model-fitting             [pixel**2]
#ELLIP1MODEL_IMAGE        Ellipticity component from model-fitting                 
#ELLIP2MODEL_IMAGE        Ellipticity component from model-fitting                 
#POLAR1MODEL_IMAGE        Ellipticity component (quadratic) from model-fitting     
#POLAR2MODEL_IMAGE        Ellipticity component (quadratic) from model-fitting     
#ELLIP1ERRMODEL_IMAGE     Ellipticity component std.error from model-fitting       
#ELLIP2ERRMODEL_IMAGE     Ellipticity component std.error from model-fitting       
#ELLIPCORRMODEL_IMAGE     Corr.coeff between ellip.components from model-fitting   
#POLAR1ERRMODEL_IMAGE     Polarisation component std.error from model-fitting      
#POLAR2ERRMODEL_IMAGE     Polarisation component std.error from model-fitting      
#POLARCORRMODEL_IMAGE     Corr.coeff between polar. components from fitting        
#X2MODEL_WORLD            Variance along X-WORLD (alpha) from model-fitting         [deg**2]
#Y2MODEL_WORLD            Variance along Y_WORLD (delta) from model-fitting         [deg**2]
#XYMODEL_WORLD            Covariance between X-WORLD and Y-WORLD from model-fitting [deg**2]
#ELLIP1MODEL_WORLD        Ellipticity component from model-fitting                 
#ELLIP2MODEL_WORLD        Ellipticity component from model-fitting                 
#POLAR1MODEL_WORLD        Polarisation component from model-fitting                
#POLAR2MODEL_WORLD        Polarisation component from model-fitting                
#ELLIP1ERRMODEL_WORLD     Ellipticity component std.error from model-fitting       
#ELLIP2ERRMODEL_WORLD     Ellipticity component std.error from model-fitting       
#ELLIPCORRMODEL_WORLD     Corr.coeff between ellip.components from model-fitting   
#POLAR1ERRMODEL_WORLD     Polarisation component std.error from model-fitting      
#POLAR2ERRMODEL_WORLD     Polarisation component std.error from model-fitting      
#POLARCORRMODEL_WORLD     Corr.coeff between polar. components from fitting        
#CXXMODEL_IMAGE           Cxx ellipse parameter from model-fitting                  [pixel**(-2)]
#CYYMODEL_IMAGE           Cyy ellipse parameter from model-fittinh                  [pixel**(-2)]
#CXYMODEL_IMAGE           Cxy ellipse parameter from model-fitting                  [pixel**(-2)]
#CXXMODEL_WORLD           Cxx ellipse parameter (WORLD) from model-fitting          [deg**(-2)]
#CYYMODEL_WORLD           Cyy ellipse parameter (WORLD) from model-fitting          [deg**(-2)]
#CXYMODEL_WORLD           Cxy ellipse parameter (WORLD) from model-fitting          [deg**(-2)]
#AMODEL_IMAGE             Model RMS along major axis                                [pixel]
#BMODEL_IMAGE             Model RMS along minor axis                                [pixel]
#THETAMODEL_IMAGE         Model position angle (CCW/x)                              [deg]
#AMODEL_WORLD             Model RMS along major axis (WORLD units)                  [deg]
#BMODEL_WORLD             Model RMS along minor axis (WORLD units)                  [deg]
#THETAMODEL_WORLD         Model position angle (CCW/WORLD-x)                        [deg]
#THETAMODEL_SKY           Model position angle (east of north) (native)             [deg]
#THETAMODEL_J2000         Model position angle (east of north) (J2000)              [deg]
#THETAMODEL_B1950         Model position angle (east of north) (B1950)              [deg]
#SPREAD_MODEL             Spread parameter from model-fitting                      
#SPREADERR_MODEL          Spread parameter error from model-fitting                
#FLUX_BACKOFFSET          Background offset from fitting                            [count]
#FLUXERR_BACKOFFSET       RMS error on fitted background offset                     [count]
#FLUX_POINTSOURCE         Point source flux from fitting                            [count]
#FLUXERR_POINTSOURCE      RMS error on fitted point source total flux               [count]
#MAG_POINTSOURCE          Point source total magnitude from fitting                 [mag]
#MAGERR_POINTSOURCE       RMS error on fitted point source total magnitude          [mag]
#FLUX_SPHEROID            Spheroid total flux from fitting                          [count]
#FLUXERR_SPHEROID         RMS error on fitted spheroid total flux                   [count]
#MAG_SPHEROID             Spheroid total magnitude from fitting                     [mag]
#MAGERR_SPHEROID          RMS error on fitted spheroid total magnitude              [mag]
#FLUX_MAX_SPHEROID        Peak spheroid flux above background                       [count]
#FLUX_EFF_SPHEROID        Effective spheroid flux above background                  [count]
#FLUX_MEAN_SPHEROID       Mean effective spheroid flux above background             [count]
#MU_MAX_SPHEROID          Peak spheroid surface brightness above background         [mag * arcsec**(-2)]
#MU_EFF_SPHEROID          Effective spheroid surface brightness above background    [mag * arcsec**(-2)]
#MU_MEAN_SPHEROID         Mean effective spheroid surface brightness above backgrou [mag * arcsec**(-2)]
#SPHEROID_REFF_IMAGE      Spheroid effective radius from fitting                    [pixel]
#SPHEROID_REFFERR_IMAGE   RMS error on fitted spheroid effective radius             [pixel]
#SPHEROID_REFF_WORLD      Spheroid effective radius from fitting                    [deg]
#SPHEROID_REFFERR_WORLD   RMS error on fitted spheroid effective radius             [deg]
#SPHEROID_ASPECT_IMAGE    Spheroid aspect ratio from fitting                       
#SPHEROID_ASPECTERR_IMAGE RMS error on fitted spheroid aspect ratio                
#SPHEROID_ASPECT_WORLD    Spheroid aspect ratio from fitting                       
#SPHEROID_ASPECTERR_WORLD RMS error on fitted spheroid aspect ratio                
#SPHEROID_THETA_IMAGE     Spheroid position angle (CCW/x) from fitting              [deg]
#SPHEROID_THETAERR_IMAGE  RMS error on spheroid position angle                      [deg]
#SPHEROID_THETA_WORLD     Spheroid position angle (CCW/world-x)                     [deg]
#SPHEROID_THETAERR_WORLD  RMS error on spheroid position angle                      [deg]
#SPHEROID_THETA_SKY       Spheroid position angle (east of north, native)           [deg]
#SPHEROID_THETA_J2000     Spheroid position angle (east of north, J2000)            [deg]
#SPHEROID_THETA_B1950     Spheroid position angle (east of north, B1950)            [deg]
#SPHEROID_SERSICN         Spheroid Sersic index from fitting                       
#SPHEROID_SERSICNERR      RMS error on fitted spheroid Sersic index                
#FLUX_DISK                Disk total flux from fitting                              [count]
#FLUXERR_DISK             RMS error on fitted disk total flux                       [count]
#MAG_DISK                 Disk total magnitude from fitting                         [mag]
#MAGERR_DISK              RMS error on fitted disk total magnitude                  [mag]
#FLUX_MAX_DISK            Peak disk flux above background                           [count]
#FLUX_EFF_DISK            Effective disk flux above background                      [count]
#FLUX_MEAN_DISK           Mean effective disk flux above background                 [count]
#MU_MAX_DISK              Peak disk surface brightness above background             [mag * arcsec**(-2)]
#MU_EFF_DISK              Effective disk surface brightness above background        [mag * arcsec**(-2)]
#MU_MEAN_DISK             Mean effective disk surface brightness above background   [mag * arcsec**(-2)]
#DISK_SCALE_IMAGE         Disk scalelength from fitting                             [pixel]
#DISK_SCALEERR_IMAGE      RMS error on fitted disk scalelength                      [pixel]
#DISK_SCALE_WORLD         Disk scalelength from fitting (world coords)              [deg]
#DISK_SCALEERR_WORLD      RMS error on fitted disk scalelength (world coords)       [deg]
#DISK_ASPECT_IMAGE        Disk aspect ratio from fitting                           
#DISK_ASPECTERR_IMAGE     RMS error on fitted disk aspect ratio                    
#DISK_ASPECT_WORLD        Disk aspect ratio from fitting                           
#DISK_ASPECTERR_WORLD     RMS error on disk aspect ratio                           
#DISK_INCLINATION         Disk inclination from fitting                             [deg]
#DISK_INCLINATIONERR      RMS error on disk inclination from fitting                [deg]
#DISK_THETA_IMAGE         Disk position angle (CCW/x) from fitting                  [deg]
#DISK_THETAERR_IMAGE      RMS error on fitted disk position angle                   [deg]
#DISK_THETA_WORLD         Disk position angle (CCW/world-x)                         [deg]
#DISK_THETAERR_WORLD      RMS error on disk position angle                          [deg]
#DISK_THETA_SKY           Disk position angle (east of north, native)               [deg]
#DISK_THETA_J2000         Disk position angle (east of north, J2000)                [deg]
#DISK_THETA_B1950         Disk position angle (east of north, B1950)                [deg]
