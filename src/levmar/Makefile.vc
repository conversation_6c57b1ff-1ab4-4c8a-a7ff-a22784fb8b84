#
# MS Visual C Makefile for <PERSON><PERSON> - Marquardt minimization
# Under Unix/Linux, use <PERSON><PERSON>le for GCC
#
# At the command prompt, type
# nmake /f Makefile.vc
#
# NOTE: To use this, you must have MSVC installed and properly
# configured for command line use (you might need to run VCVARS32.BAT
# included with your copy of MSVC). Another option is to use the
# free MSVC toolkit from http://msdn.microsoft.com/visualc/vctoolkit2003/
#

MAKE=nmake /nologo
CC=cl /nologo
CONFIGFLAGS=#/ULINSOLVERS_RETAIN_MEMORY
# YOU MIGHT WANT TO UNCOMMENT THE FOLLOWING LINE
#SPOPTFLAGS=/GL /G7 /arch:SSE2 # special optimization: resp. whole program opt., Athlon/Pentium4 opt., SSE2 extensions
# /MD COMPILES WITH DYNAMIC LINK SUPPORT. FOR STATIC LINK, SUBSTITUTE WITH /MT
# FLAG /EHsc SUPERSEDED /GX IN MSVC'05. IF YOU HAVE AN EARLIER VERSION THAT COMPLAINS ABOUT IT, CHANGE /EHsc TO /GX
CFLAGS=$(CONFIGFLAGS) /I. /MD /TC /W3 /EHsc /D_CRT_SECURE_NO_WARNINGS /O2 $(SPOPTFLAGS) # /Wall
LAPACKLIBS_PATH=C:\src\lib # WHEN USING LAPACK, CHANGE THIS TO WHERE YOUR COMPILED LIBS ARE!
LDFLAGS=/link /subsystem:console /opt:ref /libpath:$(LAPACKLIBS_PATH) /libpath:.
LIBOBJS=lm.obj Axb.obj misc.obj lmlec.obj lmbc.obj lmblec.obj lmbleic.obj
LIBSRCS=lm.c Axb.c misc.c lmlec.c lmbc.c lmblec.c lmbleic.c
DEMOBJS=lmdemo.obj
DEMOSRCS=lmdemo.c
AR=lib /nologo

# comment the following line if you are not using LAPACK
LAPACKLIBS=clapack.lib blas.lib libF77.lib libI77.lib

LIBS=levmar.lib $(LAPACKLIBS)

all: levmar.lib lmdemo.exe

levmar.lib: $(LIBOBJS)
	$(AR) /out:levmar.lib $(LIBOBJS)

lmdemo.exe: $(DEMOBJS) levmar.lib
	$(CC) $(DEMOBJS) $(LDFLAGS) /out:lmdemo.exe /incremental:no $(LIBS)

lm.obj: lm.c lm_core.c levmar.h misc.h compiler.h
Axb.obj: Axb.c Axb_core.c levmar.h misc.h
misc.obj: misc.c misc_core.c levmar.h misc.h
lmlec.obj: lmlec.c lmlec_core.c levmar.h misc.h
lmbc.obj: lmbc.c lmbc_core.c levmar.h misc.h  compiler.h
lmblec.obj: lmblec.c lmblec_core.c levmar.h misc.h
lmbleic.obj: lmbleic.c lmbleic_core.c levmar.h misc.h

lmdemo.obj: levmar.h

clean:
	-del $(LIBOBJS) $(DEMOBJS)

cleanall: clean
	-del lmdemo.exe
	-del levmar.lib

