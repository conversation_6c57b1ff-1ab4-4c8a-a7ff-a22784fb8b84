#
#				Makefile.am
#
# LevMar Makefile.am. Process this file with automake to generate a Makefile
#
#%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
#
#	This file part of:	AstrOmatic software
#
#	Copyright:		(C) 1994,1997 ESO
#	          		(C) 1995,1996 Leiden Observatory 
#	          		(C) 1998-2021 IAP/CNRS/SorbonneU
#	          		(C) 2021-2023 CFHT/CNRS
#	          		(C) 2023-2025 CEA/AIM/UParisSaclay
#	          		(C) 2004-2011 Man<PERSON> Lourakis (orig. LevMar)
#
#	Licenses:		GNU General Public License
#
#	AstrOmatic software is free software: you can redistribute it and/or
#	modify it under the terms of the GNU General Public License as
#	published by the Free Software Foundation, either version 3 of the
#	License, or (at your option) any later version.
#	AstrOmatic software is distributed in the hope that it will be useful,
#	but WITHOUT ANY WARRANTY; without even the implied warranty of
#	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#	GNU General Public License for more details.
#	You should have received a copy of the GNU General Public License
#	along with AstrOmatic software.
#	If not, see <http://www.gnu.org/licenses/>.
#
#	Last modified:		10/07/2012
#
#%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

noinst_LIBRARIES	= liblevmar.a
liblevmar_a_SOURCES	= Axb.c lmbc.c lm.c lmblec.c lmbleic.c lmlec.c misc.c \
			  compiler.h levmar.h misc.h
EXTRA_liblevmar_a_SOURCES = Axb_core.c lmbc_core.c lm_core.c \
			  lmblec_core.c lmbleic_core.c lmlec_core.c \
			  misc_core.c \
			  LICENSE README README.txt \
			  Makefile.icc Makefile.vc expfit.c levmar.h.in \
			  lmdemo.c lm.h matlab

