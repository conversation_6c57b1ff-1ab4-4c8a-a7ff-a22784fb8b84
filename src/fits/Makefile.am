#
#				Makefile.am
#
# Process this file with automake to generate a Makefile
#
#%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
#
#	This file part of:	AstrOmatic FITS/LDAC library
#
#	Copyright:		(C) 2002-2010 <PERSON> -- IAP/CNRS/UPMC
#
#	Last modified:		09/10/2010
#
#	License:		GNU General Public License
#
#	AstrOmatic software is free software: you can redistribute it and/or
#	modify it under the terms of the GNU General Public License as
#	published by the Free Software Foundation, either version 3 of the
#	License, or (at your option) any later version.
#	AstrOmatic software is distributed in the hope that it will be useful,
#	but WITHOUT ANY WARRANTY; without even the implied warranty of
#	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#	GNU General Public License for more details.
#	You should have received a copy of the GNU General Public License
#	along with AstrOmatic software.
#	If not, see <http://www.gnu.org/licenses/>.
#
#%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

noinst_LIBRARIES	= libfits.a
libfits_a_SOURCES	= fitsbody.c fitscat.c fitscheck.c fitscleanup.c \
			  fitsconv.c fitshead.c fitskey.c fitsmisc.c \
			  fitsread.c fitstab.c fitsutil.c fitswrite.c \
			  fitscat_defs.h fitscat.h
