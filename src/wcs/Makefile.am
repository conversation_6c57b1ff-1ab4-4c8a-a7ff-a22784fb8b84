#
#				Makefile.am
#
# WCSlib Makefile.am. Process this file with automake to generate a Makefile
#
#%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
#
#	This file part of:	AstrOmatic WCS library
#
#	Copyright:		(C) 1994,1997 ESO
#	          		(C) 1995,1996 Leiden Observatory 
#	          		(C) 1998-2021 IAP/CNRS/SorbonneU
#	          		(C) 2021-2023 CFHT/CNRS
#	          		(C) 2023-2025 CEA/AIM/UParisSaclay
#	          		(C) 1995-1999 Mark Calabretta (original WCSlib)
#
#	Licenses:		GNU General Public License (this file)
#				GNU Library General Public License (WCSlib)
#
#	AstrOmatic software is free software: you can redistribute it and/or
#	modify it under the terms of the GNU General Public License as
#	published by the Free Software Foundation, either version 3 of the
#	License, or (at your option) any later version.
#	AstrOmatic software is distributed in the hope that it will be useful,
#	but WITHOUT ANY WARRANTY; without even the implied warranty of
#	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#	GNU General Public License for more details.
#	You should have received a copy of the GNU General Public License
#	along with AstrOmatic software.
#	If not, see <http://www.gnu.org/licenses/>.
#
#	Last modified:		02/12/2015
#
#%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

noinst_LIBRARIES	= libwcs_c.a
libwcs_c_a_SOURCES	= cel.c lin.c poly.c proj.c sph.c tnx.c wcs.c \
			  wcstrig.c \
			  cel.h lin.h poly.h proj.h sph.h tnx.h wcs.h \
			  wcsmath.h wcstrig.h
EXTRA_DIST		= LICENSE README

