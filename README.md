# SExtractor

[![Build Status](https://travis-ci.org/astromatic/sextractor.svg?branch=master)](https://travis-ci.org/astromatic/sextractor)
[![Coverity Scan Build Status](https://scan.coverity.com/projects/sextractor/badge.svg)](https://scan.coverity.com/projects/sextractor "Coverity Badge")
[![Documentation Status](https://github.com/astromatic/sextractor/actions/workflows/doc.yml/badge.svg)](https://github.com/astromatic/sextractor/actions/workflows/doc.yml)

[SExtractor] stands for ``Source Extractor'': a software for extracting catalogs of sources from astronomical images.

Check out the on-line [documentation], the [official web page], and the [discussion forum].

[SExtractor]: https://astromatic.net/software/sextractor
[documentation]: https://astromatic.github.io/sextractor
[official web page]: https://astromatic.net/software/sextractor
[discussion forum]: https://github.com/astromatic/sextractor/discussions

