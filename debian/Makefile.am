#
#				Makefile.am
#
# Main Makefile. Process this file with automake to generate a Makefile
#
#%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
#
#	This file part of:	an AstrOmatic package
#
#	Copyright:		(C) 2017 IAP/CNRS/UPMC
#
#	License:		GNU General Public License
#
#	AstrOmatic software is free software: you can redistribute it and/or
#	modify it under the terms of the GNU General Public License as published
#	by the Free Software Foundation, either version 3 of the License, or
# 	(at your option) any later version.
#	AstrOmatic software is distributed in the hope that it will be useful,
#	but WITHOUT ANY WARRANTY; without even the implied warranty of
#	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#	GNU General Public License for more details.
#	You should have received a copy of the GNU General Public License
#	along with AstrOmatic software. If not, see
#	<http://www.gnu.org/licenses/>.
#
#	Last modified:		13/06/2017
#
#%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

EXTRA_DIST		= compat control rules

