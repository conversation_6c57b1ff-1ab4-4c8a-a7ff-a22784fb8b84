name: doc

on:
  push:
    paths:
      - 'doc/**'
  pull_request:
    paths:
      - 'doc/**'
  workflow_dispatch:

jobs:

#############
# Build doc #
#############
  build:
    name: Make doc
    continue-on-error: false
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Install dependencies
        run: |
          pip3 install -r doc/requirements.txt
          sudo apt-get -y install latexmk texlive-latex-recommended texlive-fonts-recommended texlive-formats-extra

      - name: Build doc
        run: |
          ./autogen.sh
          ./configure --disable-model-fitting --disable-cfitsio --disable-threads
          cd doc
          make html
          make latexpdf
          cp build/latex/sextractor.pdf build/html/

      - name: Set destination dir
        if: github.ref_name != 'master'
        run: |
          echo "DOC_DEST=${{ github.ref_name }}" >> $GITHUB_ENV

      - name: Deploy
        if: success()
        uses: peaceiris/actions-gh-pages@v3
        with:
          publish_branch: doc-pages
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: doc/build/html/
          destination_dir: ${{ env.DOC_DEST }}


