# autogen.sh generated files
/Makefile.in
/aclocal.m4
/autoconf/
/autom4te.cache/
/config.h.in
/configure
/debian/Makefile.in
/doc/Makefile.in
/m4/libtool.m4
/m4/ltoptions.m4
/m4/ltsugar.m4
/m4/ltversion.m4
/m4/lt~obsolete.m4
/man/Makefile.in
/src/Makefile.in
/src/fits/Makefile.in
/src/wcs/Makefile.in
/src/levmar/Makefile.in
/tests/Makefile.in

# configure generated files
/Makefile
/config.h
/config.h.in~
/config.log
/config.status
/debian/Makefile
/debian/changelog
/doc/Makefile
/libtool
/man/Makefile
/sextractor.spec
/src/.deps/
/src/levmar/.deps/
/src/Makefile
/src/fits/.deps/
/src/fits/Makefile
/src/wcs/.deps/
/src/wcs/Makefile
/src/levmar/Makefile
/tests/Makefile
/stamp-h1
/man/sex.1

# build objects and targets
*.o
/src/sex
/src/ldactoasc
/src/f
/src/fits/libfits.a
/src/wcs/libwcs_c.a
/src/levmar/liblevmar.a
/doc/build/

# generated by icc with -prof-gen=srcpos option
*.dyn
*.spi
*.spl
