.. File License.rst

.. include:: global.rst

License
=======

Code
----

The |SExtractor|_ code is licensed under a `GPL v3 license <https://www.gnu.org/licenses/gpl.html>`_:

|SExtractor| *is free software: you can redistribute it and/or modify it under the terms of the GNU General Public License as published by the Free Software Foundation, either version 3 of the License, or (at your option) any later version.*

|SExtractor| *is distributed in the hope that it will be useful, but WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
See the GNU General Public License for more details.*

Documentation
-------------

.. _fig_license:

.. figure:: figures/by-sa.*
   :figwidth: 20%
   :align: center


This documentation and its content are licensed under a `Creative Commons Attribution-ShareAlike 4.0 International License <https://creativecommons.org/licenses/by-sa/4.0/>`_:

**Attribution** — *You must give appropriate credit, provide a link to the license, and indicate if changes were made.
You may do so in any reasonable manner, but not in any way that suggests the licensor endorses you or your use.*

**ShareAlike** — *If you remix, transform, or build upon the material, you must distribute your contributions under the same license as the original.*

**No additional restrictions** — *You may not apply legal terms or technological measures that legally restrict others from doing anything the license permits*.


