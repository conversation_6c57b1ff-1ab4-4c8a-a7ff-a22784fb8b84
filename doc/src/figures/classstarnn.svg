<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->
<svg width="880.12" height="590.37" version="1.1" viewBox="0 0 880.12048 590.37109" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"><metadata><rdf:RDF><cc:Work rdf:about=""><dc:format>image/svg+xml</dc:format><dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"/><dc:title/></cc:Work></rdf:RDF></metadata><g transform="matrix(1.3333 0 0 1.3333 -4.5746 1.8845e-7)"><g transform="matrix(0,1,1,0,0,0)"><path d="m131.9 185.06h-14.398c-3.602 0-7.203 3.601-7.203 7.199v14.402c0 3.598 3.601 7.199 7.203 7.199h14.398c3.602 0 7.2-3.601 7.2-7.199v-14.402c0-3.598-3.598-7.199-7.2-7.199" fill="#d3d2d2" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m175.1 185.06h-14.399c-3.601 0-7.199 3.601-7.199 7.199v14.402c0 3.598 3.598 7.199 7.199 7.199h14.399c3.601 0 7.199-3.601 7.199-7.199v-14.402c0-3.598-3.598-7.199-7.199-7.199" fill="#d3d2d2" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m88.699 185.06h-14.402c-3.598 0-7.199 3.601-7.199 7.199v14.402c0 3.598 3.601 7.199 7.199 7.199h14.402c3.598 0 7.199-3.601 7.199-7.199v-14.402c0-3.598-3.601-7.199-7.199-7.199" fill="#d3d2d2" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m218.3 185.06h-14.399c-3.601 0-7.199 3.601-7.199 7.199v14.402c0 3.598 3.598 7.199 7.199 7.199h14.399c3.601 0 7.203-3.601 7.203-7.199v-14.402c0-3.598-3.602-7.199-7.203-7.199" fill="#d3d2d2" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m261.5 185.06h-14.402c-3.598 0-7.2 3.601-7.2 7.199v14.402c0 3.598 3.602 7.199 7.2 7.199h14.402c3.598 0 7.199-3.601 7.199-7.199v-14.402c0-3.598-3.601-7.199-7.199-7.199" fill="#d3d2d2" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m304.7 185.06h-14.402c-3.598 0-7.199 3.601-7.199 7.199v14.402c0 3.598 3.601 7.199 7.199 7.199h14.402c3.598 0 7.199-3.601 7.199-7.199v-14.402c0-3.598-3.601-7.199-7.199-7.199" fill="#d3d2d2" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m347.9 185.06h-14.398c-3.602 0-7.203 3.601-7.203 7.199v14.402c0 3.598 3.601 7.199 7.203 7.199h14.398c3.602 0 7.2-3.601 7.2-7.199v-14.402c0-3.598-3.598-7.199-7.2-7.199" fill="#d3d2d2" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m391.09 185.06h-14.402c-3.598 0-7.2 3.601-7.2 7.199v14.402c0 3.598 3.602 7.199 7.2 7.199h14.402c3.598 0 7.199-3.601 7.199-7.199v-14.402c0-3.598-3.601-7.199-7.199-7.199" fill="#d3d2d2" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m434.28 185.06h-14.398c-3.602 0-7.203 3.601-7.203 7.199v14.402c0 3.598 3.601 7.199 7.203 7.199h14.398c3.602 0 7.199-3.601 7.199-7.199v-14.402c0-3.598-3.597-7.199-7.199-7.199" fill="#d3d2d2" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m45.5 185.06h-14.402c-3.598 0-7.2 3.601-7.2 7.199v14.402c0 3.598 3.602 7.199 7.2 7.199h14.402c3.598 0 7.199-3.601 7.199-7.199v-14.402c0-3.598-3.601-7.199-7.199-7.199" fill="#d3d2d2" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m38.297 300.25c-8.242 0-14.399 9.258-14.399 21.601 0 12.344 6.157 21.598 14.399 21.598 8.246 0 14.402-9.254 14.402-21.598 0-12.343-6.156-21.601-14.402-21.601" fill="#d3d2d2" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m81.5 300.25c-8.246 0-14.402 9.258-14.402 21.601 0 12.344 6.156 21.598 14.402 21.598 8.242 0 14.398-9.254 14.398-21.598 0-12.343-6.156-21.601-14.398-21.601" fill="#d3d2d2" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m124.7 300.25c-8.246 0-14.402 9.258-14.402 21.601 0 12.344 6.156 21.598 14.402 21.598 8.242 0 14.399-9.254 14.399-21.598 0-12.343-6.157-21.601-14.399-21.601" fill="#d3d2d2" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m167.9 300.25c-8.246 0-14.398 9.258-14.398 21.601 0 12.344 6.152 21.598 14.398 21.598 8.243 0 14.399-9.254 14.399-21.598 0-12.343-6.156-21.601-14.399-21.601" fill="#d3d2d2" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m211.09 300.25c-8.246 0-14.402 9.258-14.402 21.601 0 12.344 6.156 21.598 14.402 21.598 8.242 0 14.398-9.254 14.398-21.598 0-12.343-6.156-21.601-14.398-21.601" fill="#d3d2d2" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m225.48 429.86c-8.242 0-14.398 9.254-14.398 21.598s6.156 21.602 14.398 21.602c8.246 0 14.403-9.258 14.403-21.602s-6.157-21.598-14.403-21.598" fill="#d3d2d2" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m254.3 300.25c-8.242 0-14.399 9.258-14.399 21.601 0 12.344 6.157 21.598 14.399 21.598 8.246 0 14.402-9.254 14.402-21.598 0-12.343-6.156-21.601-14.402-21.601" fill="#d3d2d2" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m297.5 300.25c-8.246 0-14.402 9.258-14.402 21.601 0 12.344 6.156 21.598 14.402 21.598 8.242 0 14.398-9.254 14.398-21.598 0-12.343-6.156-21.601-14.398-21.601" fill="#d3d2d2" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m340.7 300.25c-8.246 0-14.402 9.258-14.402 21.601 0 12.344 6.156 21.598 14.402 21.598 8.242 0 14.399-9.254 14.399-21.598 0-12.343-6.157-21.601-14.399-21.601" fill="#d3d2d2" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m383.89 300.25c-8.246 0-14.403 9.258-14.403 21.601 0 12.344 6.157 21.598 14.403 21.598 8.242 0 14.398-9.254 14.398-21.598 0-12.343-6.156-21.601-14.398-21.601" fill="#d3d2d2" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m427.09 300.25c-8.246 0-14.402 9.258-14.402 21.601 0 12.344 6.156 21.598 14.402 21.598 8.242 0 14.398-9.254 14.398-21.598 0-12.343-6.156-21.601-14.398-21.601" fill="#d3d2d2" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m340.7 213.86-43.199 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m297.5 213.86-43.203 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m254.3 213.86-43.199 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m211.1 213.86-43.2 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m167.9 213.86-43.199 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m124.7 213.86-43.199 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m81.5 213.86-43.203 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m340.7 213.86-86.402 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m297.5 213.86-86.402 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m254.3 213.86-86.399 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m211.1 213.86-86.399 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m167.9 213.86-86.398 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m124.7 213.86-86.402 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m340.7 213.86-129.6 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m297.5 213.86-129.6 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m254.3 213.86-129.6 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m211.1 213.86-129.6 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m167.9 213.86-129.6 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m340.7 213.86-172.8 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m297.5 213.86-172.8 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m254.3 213.86-172.8 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m211.1 213.86-172.8 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m340.7 213.86-216 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m297.5 213.86-216 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m254.3 213.86-216 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m340.7 213.86-259.2 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m297.5 213.86-259.2 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m340.7 213.86-302.4 86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m340.7 300.25-43.199-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m297.5 300.25-43.203-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m254.3 300.25-43.199-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m211.1 300.25-43.2-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m167.9 300.25-43.199-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m124.7 300.25-43.199-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m81.5 300.25-43.203-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m340.7 300.25-86.402-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m297.5 300.25-86.402-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m254.3 300.25-86.399-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m211.1 300.25-86.399-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m167.9 300.25-86.398-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m124.7 300.25-86.402-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m340.7 300.25-129.6-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m297.5 300.25-129.6-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m254.3 300.25-129.6-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m211.1 300.25-129.6-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m167.9 300.25-129.6-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m340.7 300.25-172.8-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m297.5 300.25-172.8-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m254.3 300.25-172.8-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m211.1 300.25-172.8-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m340.7 300.25-216-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m297.5 300.25-216-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m254.3 300.25-216-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m340.7 300.25-259.2-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m297.5 300.25-259.2-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m340.7 300.25-302.4-86.399" fill="#231f20" fill-rule="evenodd" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m340.7 213.86v86.399" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m297.52 213.86v86.399" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m254.32 213.86v86.399" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m211.12 213.86v86.399" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m167.92 213.86v86.399" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m124.72 213.86v86.399" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m81.516 213.86v86.399" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m38.316 213.86v86.399" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m38.293 55.453v57.582" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m81.5 55.453v57.582" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m124.7 55.453v57.582" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m167.9 55.453v57.582" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m211.1 55.453v57.582" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m254.3 55.453v57.582" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m297.5 55.453v57.582" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m340.7 55.453v57.582" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m38.297 141.86v43.18" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m81.5 141.86v43.18" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m124.7 141.86v43.18" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m167.9 141.86v43.18" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m211.1 141.86v43.18" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m254.3 141.86v43.18" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m297.5 141.86v43.18" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m340.7 141.86v43.18" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m383.89 141.86v43.18" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m427.09 141.86v43.18" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m383.89 55.453v57.594" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><path d="m113.06 23.887v28.797l28.8-14.399" fill="#d3d2d2" fill-rule="evenodd"/><g transform="matrix(0,1,1,0,0,0)"><path d="m23.887 113.06h28.797l-14.399 28.8z" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><path d="m113.06 67.086v28.801l28.8-14.403" fill="#d3d2d2" fill-rule="evenodd"/><g transform="matrix(0,1,1,0,0,0)"><path d="m67.086 113.06h28.801l-14.403 28.8z" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><path d="m113.06 110.28v28.801l28.8-14.402" fill="#d3d2d2" fill-rule="evenodd"/><g transform="matrix(0,1,1,0,0,0)"><path d="m110.28 113.06h28.801l-14.402 28.8z" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><path d="m113.06 153.48v28.801l28.8-14.398" fill="#d3d2d2" fill-rule="evenodd"/><g transform="matrix(0,1,1,0,0,0)"><path d="m153.48 113.06h28.801l-14.398 28.8z" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><path d="m113.06 196.68v28.8l28.8-14.398" fill="#d3d2d2" fill-rule="evenodd"/><g transform="matrix(0,1,1,0,0,0)"><path d="m196.68 113.06h28.8l-14.398 28.8z" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><path d="m113.06 239.89v28.797l28.8-14.399" fill="#d3d2d2" fill-rule="evenodd"/><g transform="matrix(0,1,1,0,0,0)"><path d="m239.89 113.06h28.797l-14.399 28.8z" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><path d="m113.06 283.09v28.801l28.8-14.403" fill="#d3d2d2" fill-rule="evenodd"/><g transform="matrix(0,1,1,0,0,0)"><path d="m283.09 113.06h28.801l-14.403 28.8z" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><path d="m113.06 326.28v28.801l28.8-14.402" fill="#d3d2d2" fill-rule="evenodd"/><g transform="matrix(0,1,1,0,0,0)"><path d="m326.28 113.06h28.801l-14.402 28.8z" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><path d="m113.06 369.48v28.801l28.8-14.398" fill="#d3d2d2" fill-rule="evenodd"/><g transform="matrix(0,1,1,0,0,0)"><path d="m369.48 113.06h28.801l-14.398 28.8z" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><path d="m113.06 412.68v28.8l28.8-14.398" fill="#d3d2d2" fill-rule="evenodd"/><g transform="matrix(0,1,1,0,0,0)"><path d="m412.68 113.06h28.8l-14.398 28.8z" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width="1.6"/></g><g fill="#231f20" font-family="'Nimbus Roman'" font-style="italic"><text transform="translate(26.654 344.23)" font-size="19.2px"><tspan x="0" y="0">A</tspan><tspan x="0" y="-302.84998">A</tspan></text>
<text transform="translate(39.461 45.279)" font-size="12.8px"><tspan x="0" y="0">0</tspan><tspan x="-5.8503938 3.3912065 9.7912064" y="348.03101">max</tspan><tspan x="-12.806259" y="76.67498">.</tspan><tspan x="-12.806259" y="119.875">.</tspan><tspan x="-12.806259" y="163.075">.</tspan><tspan x="-12.806259" y="206.27499">.</tspan></text>
<text transform="matrix(1 0 0 .79421 26.448 295.05)" font-size="16.117px"><tspan x="0" y="0">.</tspan></text>
<text transform="translate(26.654 78.754)" font-size="12.8px"><tspan x="0" y="0">.</tspan></text>
</g><g transform="matrix(0,1,1,0,0,0)"><path d="m427.09 55.453v57.594" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m383.89 213.86v86.399" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m427.09 213.86v86.399l-43.199-86.399-43.203 86.399" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m427.09 213.86-43.199 86.399c-43.203-86.399-43.203-86.399-43.203-86.399l86.402 86.399-129.6-86.399 86.403 86.399-129.6-86.399 172.8 86.399-216-86.399 172.8 86.399-216-86.399 259.2 86.399-302.4-86.399 259.2 86.399-302.4-86.399 345.6 86.399-388.8-86.399 345.6 86.399" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m427.09 213.86-86.402 86.399" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m427.09 213.86-129.6 86.399 86.403-86.399-129.6 86.399 172.8-86.399-216 86.399 172.8-86.399-216 86.399 259.2-86.399-302.4 86.399 259.2-86.399-302.4 86.399 345.6-86.399-388.8 86.399 345.6-86.399" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m38.285 343.45 187.2 86.402-144-86.402" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m124.68 343.45 100.8 86.402-57.597-86.402" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m211.09 343.45 14.398 86.402 28.801-86.402" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m297.48 343.45-72 86.402 115.2-86.402" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m383.89 343.45-158.4 86.402 201.6-86.402" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g transform="matrix(0,1,1,0,0,0)"><path d="m225.48 473.06v86.398" fill="none" stroke="#231f20" stroke-miterlimit="10" stroke-width=".4"/></g><g fill="#231f20"><g font-family="'Nimbus Roman'" font-style="italic"><text transform="translate(567.5 229.75)" font-size="16px"><tspan x="0" y="0" font-family="'Courier 10 Pitch'" font-style="normal">CLASS_STAR</tspan></text>
<text transform="translate(39.63 349.79)" font-size="12.8px"><tspan x="0" y="0">7</tspan></text>
<text transform="translate(28.042 387.38)" font-size="19.2px"><tspan x="0" y="0">I</tspan><tspan x="-24.937498 -15.337498 -6.8126974 1.7121024 7.0497022 16.649702" y="43.306194">Seeing</tspan></text>
</g><text transform="translate(12.255 9.4848)" font-family="FreeSans" font-size="12.8px"><tspan x="0 7.1167998 12.8 17.049601 22.7456 32.703999 38.387199 41.971199 47.628799 51.878399 86.400002 94.937599 100.6208 105.6 111.2832 116.9664 120.5248 124.0832 130.4832 158.39999 162.6624 169.06239 175.4624 181.8624 185.4464 188.64639 192.1792 197.8624 204.26241 209.9456 273.60001 282.8288 286.39999 292.79999 299.20001 304.88321 311.2832 314.48318 318.0416 323.72479 330.12479 335.80801 403.20001 412.44159 418.84161 422.4256 428.79999 435.20001 438.784 441.98401 445.51682 451.20001 457.60001 463.2832" y="0">ParametersRescalingInput layerHidden layerOutput layer</tspan></text>
</g></g></svg>
