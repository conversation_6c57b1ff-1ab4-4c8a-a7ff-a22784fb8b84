.. File Output.rst

.. include:: global.rst

.. _output:

Output files
------------

Diagnostic files
~~~~~~~~~~~~~~~~

Two types of files can be generated by |SExtractor|, providing diagnostics
about the source extraction process:

* "Check-images" are FITS files containing raster images such as maps of the
  background model, apertures, etc.. The configuration parameters
  ``CHECKIMAGE_TYPE`` and ``CHECKIMAGE_NAME`` allow the user to provide a list
  of check-image types and file names, respectively, to be produced by
  |SExtractor|. A complete list of available check-image types is given in
  §[chap:paramlist].
* An |XML|_ file providing a processing summary and various statistics in
  |VOTable|_ format is written if the ``WRITE_XML`` switch is set to ``Y``
  (the default). The ``XML_NAME`` parameter can be used to change the default
  file name :file:`sex.xml`. The |XML| file can be displayed with any recent
  web browser; the |XSLT| stylesheet installed together with |SExtractor| will
  automatically translate it into a dynamic, user-friendly web-page.
  For more advanced usages (e.g., access from a
  remote web server), alternative |XSLT| translation URLs may be specified
  using the ``XSL_URL`` configuration parameter.


