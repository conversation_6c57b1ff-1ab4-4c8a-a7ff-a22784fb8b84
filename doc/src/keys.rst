.. |ADU| replace:: :abbr:`ADU (Analog-to-Digital Unit)`
.. _ADU: https://en.wikipedia.org/wiki/Analog-to-digital_converter

.. |ATLAS| replace:: :program:`ATLAS`
.. _ATLAS: http://math-atlas.sourceforge.net

.. |DAOPHOT| replace:: :program:`DAOPHOT`
.. _DAOPHOT: http://ascl.net/phpBB3/viewtopic.php?t=23410

.. |DES| replace:: :abbr:`Dark Energy Survey`
.. _DES: https://darkenergysurvey.org

.. |.dmg| replace:: :file:`.dmg`
.. _.dmg: https://en.wikipedia.org/wiki/Apple_Disk_Image

.. |FFTw| replace:: :program:`FFTw`
.. _FFTw: http://www.fftw.org

.. |FWHM| replace:: :abbr:`FWHM (Full Width at Half Maximum)`
.. _FWHM: https://en.wikipedia.org/wiki/Full_width_at_half_maximum

.. |FITS| replace:: :abbr:`FITS (Flexible Image Transport System)`
.. _FITS: http://fits.gsfc.nasa.gov

.. |fv| replace:: :program:`fv`
.. _fv: http://heasarc.gsfc.nasa.gov/ftools/fv/

.. |GitHub| replace:: GitHub
.. _GitHub: https://github.com

.. |Intel| replace:: Intel\ :sup:`®`\ 
.. _Intel: http://intel.com

.. |I/O| replace:: :abbr:`I/O (Input/Output)`
.. _I/O: https://en.wikipedia.org/wiki/Input/output

.. |LevMar| replace:: :program:`LevMar`
.. _LevMar: http://users.ics.forth.gr/~lourakis/levmar

.. |MEF| replace:: :abbr:`MEF (Multi-Extension FITS)`
.. _MEF: http://www.stsci.edu/hst/HST_overview/documents/datahandbook/intro_ch23.html

.. |MKL| replace:: :abbr:`MKL (Math Kernel Library)`
.. _MKL: http://software.intel.com/intel-mkl

.. |OSX| replace:: Apple OS X\ :sup:`®`\ 
.. _OSX: http://www.apple.com/osx

.. |pdf| replace:: :abbr:`pdf (Probability Density Function)`
.. _pdf: https://en.wikipedia.org/wiki/Probability_density_function

.. |PSF| replace:: :abbr:`PSF (Point Spread Function)`
.. _PSF: https://en.wikipedia.org/wiki/Point_spread_function

.. |PSFEx| replace:: :program:`PSFEx`
.. _PSFEx: http://astromatic.net/software/psfex

.. |QSO| replace:: :abbr:`QSO (Quasi-Stellar Object)`
.. _QSO: https://en.wikipedia.org/wiki/Quasar

.. |RPM| replace:: :program:`RPM`
.. _RPM: http://www.rpm.org

.. |RMS| replace:: :abbr:`RMS (Root Mean Square)`
.. _RMS: https://en.wikipedia.org/wiki/Root_mean_square

.. |SCAMP| replace:: :program:`SCAMP`
.. _SCAMP: http://astromatic.net/software/scamp

.. |SExtractor| replace:: :program:`SExtractor`
.. _SExtractor: http://astromatic.net/software/sextractor

.. |SkyMaker| replace:: :program:`SkyMaker`
.. _SkyMaker: http://astromatic.net/software/skymaker

.. |seeing| replace:: *seeing*
.. _seeing: https://en.wikipedia.org/wiki/Astronomical_seeing

.. |SNR| replace:: :abbr:`SNR (Signal-to-Noise Ratio)`
.. _SNR: https://en.wikipedia.org/wiki/Signal-to-noise_ratio

.. |SWarp| replace:: :program:`SWarp`
.. _SWarp: http://astromatic.net/software/swarp

.. |TPV| replace:: ``TPV``
.. _TPV: fits.gsfc.nasa.gov/registry/tpvwcs/tpv.html

.. |TOPCAT| replace:: :program:`TOPCAT`
.. _TOPCAT: http://www.star.bris.ac.uk/~mbt/topcat/

.. |VOTable| replace:: VOTable
.. _VOTable: http://www.ivoa.net/documents/VOTable

.. |WCS| replace:: WCS
.. _WCS: http://www.atnf.csiro.au/people/mcalabre/WCS/index.html

.. |WCSLIB| replace:: :program:`WCSLIB`
.. _WCSLIB: http://www.atnf.csiro.au/people/mcalabre/WCS/wcslib

.. |WeightWatcher| replace:: :program:`WeightWatcher`
.. _WeightWatcher: http://astromatic.net/software/weightwatcher

.. |Windows| replace:: Microsoft Windows\ :sup:`®`\ 
.. _Windows: http://www.microsoft.com/windows

.. |XML| replace:: :abbr:`XML (eXtensible Markup Language)`
.. _XML: http://en.wikipedia.org/wiki/XML

.. |XSLT| replace:: :abbr:`XSLT (eXtensible Stylesheet Language Transformations)`
.. _XSLT: http://en.wikipedia.org/wiki/XSLT

